(function(){"use strict";var e={7816:function(e,t,s){var a=s(5471),r=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("router-view"),e._m(0)],1)},i=[function(){var e=this,t=e._self._c;return t("footer",{staticClass:"global-footer"},[t("div",{staticClass:"footer-content"},[t("p",[e._v("© 2025 永盛制冷维修有限公司-家电售卖与维修 | CHENKK-开发")]),t("p",{staticClass:"icp-info"},[e._v("备案号：（暂时为空）")])])])}],l={name:"App"},o=l,n=s(1656),c=(0,n.A)(o,r,i,!1,null,null,null),d=c.exports,u=s(173),p=s(5353),m=s(4335);a["default"].use(p.Ay);var h=new p.Ay.Store({state:{user:JSON.parse(localStorage.getItem("user"))||null,token:localStorage.getItem("token")||null,orders:[],availableOrders:[],statistics:{},loading:!1},getters:{isAuthenticated:e=>!!e.token,currentUser:e=>e.user,userType:e=>e.user?.userType||null,userOrders:e=>e.orders,availableOrders:e=>e.availableOrders,statistics:e=>e.statistics},mutations:{SET_USER(e,t){e.user=t,localStorage.setItem("user",JSON.stringify(t))},SET_TOKEN(e,t){e.token=t,localStorage.setItem("token",t)},CLEAR_AUTH(e){e.user=null,e.token=null,localStorage.removeItem("user"),localStorage.removeItem("token")},LOGOUT(e){e.user=null,e.token=null,e.userType=null,localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("userType")},SET_ORDERS(e,t){e.orders=t},SET_AVAILABLE_ORDERS(e,t){e.availableOrders=t},ADD_ORDER(e,t){e.orders.unshift(t)},SET_USERS(e,{userType:t,users:s}){e.users||(e.users={}),e.users[t]=s},UPDATE_ORDER(e,t){const s=e.orders.findIndex(e=>e.id===t.id);-1!==s&&a["default"].set(e.orders,s,t)},SET_STATISTICS(e,t){e.statistics=t},SET_LOADING(e,t){e.loading=t}},actions:{async login({commit:e},{credentials:t,userType:s}){try{e("SET_LOADING",!0),console.log("[前端] 登录请求:",{userType:s,username:t.username});const a=await m.A.post("/api/auth/login",{...t,userType:s});if(console.log("[前端] 登录响应:",a),a.success){const{user:t,token:r}=a.data;return localStorage.setItem("token",r),localStorage.setItem("user",JSON.stringify(t)),localStorage.setItem("userType",s),m.A.defaults.headers.common["Authorization"]=`Bearer ${r}`,e("SET_USER",t),e("SET_TOKEN",r),{success:!0,user:t,userType:s}}return{success:!1,message:a.message}}catch(a){return console.error("[前端] 登录失败:",a),{success:!1,message:a.response?.data?.message||"登录失败，请检查网络连接"}}finally{e("SET_LOADING",!1)}},async register({commit:e},t){try{e("SET_LOADING",!0),console.log("[前端] 注册请求:",t);const s=await m.A.post("/api/auth/register",t);return console.log("[前端] 注册响应:",s),console.log("[前端] 响应类型:",typeof s),console.log("[前端] 响应success字段:",s.success),s.success?(console.log("[前端] 注册成功，返回成功结果"),{success:!0,message:s.message}):(console.log("[前端] 注册失败，返回失败结果"),{success:!1,message:s.message})}catch(s){return console.error("[前端] 注册失败:",s),{success:!1,message:s.response?.data?.message||"注册失败，请检查网络连接"}}finally{e("SET_LOADING",!1)}},logout({commit:e}){return e("LOGOUT"),{success:!0,message:"退出登录成功"}},async fetchCustomerOrders({commit:e}){try{e("SET_LOADING",!0),console.log("[前端] 获取客户工单列表...");const t=await m.A.get("/api/customer/orders");return console.log("[前端] 获取客户工单成功:",t.data),e("SET_ORDERS",t.data.data||t.data),{success:!0}}catch(t){return console.error("[前端] 获取工单失败:",t),{success:!1,message:t.response?.data?.message||"获取工单失败"}}finally{e("SET_LOADING",!1)}},async createOrder({commit:e},t){try{e("SET_LOADING",!0),console.log("[前端] 创建工单请求:",t);const s={appliance_type:t.appliance_type,brand_model:t.brand_model,problem_description:t.problem_description,contact_name:t.contact_name,contact_phone:t.contact_phone,address:t.address,purchase_date:t.purchase_date,preferred_date:t.preferred_date,preferred_time:t.preferred_time,urgency:t.urgency,remarks:t.remarks},a=await m.A.post("/api/customer/orders",s);return console.log("[前端] 工单创建成功:",a.data),{success:!0,data:a.data.data||a.data}}catch(s){return console.error("[前端] 创建工单失败:",s),{success:!1,message:s.response?.data?.message||"创建工单失败"}}finally{e("SET_LOADING",!1)}},async fetchAvailableOrders({commit:e}){try{e("SET_LOADING",!0),console.log("[前端] 获取可接工单列表...");const t=await m.A.get("/api/worker/available-orders");return console.log("[前端] 获取可接工单成功:",t),e("SET_AVAILABLE_ORDERS",t.data||t),{success:!0}}catch(t){return console.error("[前端] 获取可接工单失败:",t),{success:!1,message:t.response?.data?.message||"获取可接工单失败"}}finally{e("SET_LOADING",!1)}},async fetchWorkerOrders({commit:e}){try{e("SET_LOADING",!0),console.log("[前端] 获取工人工单列表...");const t=await m.A.get("/api/worker/my-orders");return console.log("[前端] 获取工人工单成功:",t),e("SET_ORDERS",t.data||t),{success:!0}}catch(t){return console.error("[前端] 获取工人工单失败:",t),{success:!1,message:t.response?.data?.message||"获取工单失败"}}finally{e("SET_LOADING",!1)}},async acceptOrder({commit:e},t){try{e("SET_LOADING",!0),console.log("[前端] 接单请求:",t);const s=await m.A.post(`/api/worker/accept-order/${t}`);return console.log("[前端] 接单成功:",s),{success:!0,data:s.data||s}}catch(s){return console.error("[前端] 接单失败:",s),{success:!1,message:s.response?.data?.message||"接单失败"}}finally{e("SET_LOADING",!1)}},async fetchAllOrders({commit:e}){try{e("SET_LOADING",!0),console.log("[前端] 获取所有工单列表...");const t=await m.A.get("/api/admin/orders");return console.log("[前端] 获取所有工单成功:",t),e("SET_ORDERS",t.data||t),{success:!0}}catch(t){return console.error("[前端] 获取所有工单失败:",t),{success:!1,message:t.response?.data?.message||"获取所有工单失败"}}finally{e("SET_LOADING",!1)}},async fetchStatistics({commit:e}){try{e("SET_LOADING",!0),console.log("[前端] 获取统计数据...");const t=await m.A.get("/api/admin/statistics");return console.log("[前端] 获取统计数据成功:",t),e("SET_STATISTICS",t.data||t),{success:!0}}catch(t){return console.error("[前端] 获取统计数据失败:",t),{success:!1,message:t.response?.data?.message||"获取统计数据失败"}}finally{e("SET_LOADING",!1)}},async fetchUsers({commit:e},t){try{e("SET_LOADING",!0),console.log("[前端] 获取用户列表...",t);const s=await m.A.get(`/api/admin/users/${t}`);return console.log("[前端] 获取用户列表成功:",s),e("SET_USERS",{userType:t,users:s.data||s}),{success:!0}}catch(s){return console.error("[前端] 获取用户列表失败:",s),{success:!1,message:s.response?.data?.message||"获取用户列表失败"}}finally{e("SET_LOADING",!1)}},async createUser({commit:e},{userType:t,userData:s}){try{e("SET_LOADING",!0),console.log("[前端] 创建用户...",t,s);const a=await m.A.post(`/api/admin/users/${t}`,s);return console.log("[前端] 创建用户成功:",a),{success:!0,data:a.data||a}}catch(a){return console.error("[前端] 创建用户失败:",a),{success:!1,message:a.response?.data?.message||"创建用户失败"}}finally{e("SET_LOADING",!1)}},async updateUser({commit:e},{userType:t,userData:s}){try{e("SET_LOADING",!0),console.log("[前端] 更新用户...",t,s);const a=await m.A.put(`/api/admin/users/${t}/${s.id}`,s);return console.log("[前端] 更新用户成功:",a),{success:!0,data:a.data||a}}catch(a){return console.error("[前端] 更新用户失败:",a),{success:!1,message:a.response?.data?.message||"更新用户失败"}}finally{e("SET_LOADING",!1)}},async deleteUser({commit:e},{userType:t,userId:s}){try{e("SET_LOADING",!0),console.log("[前端] 删除用户...",t,s);const a=await m.A.delete(`/api/admin/users/${t}/${s}`);return console.log("[前端] 删除用户成功:",a),{success:!0,data:a.data||a}}catch(a){return console.error("[前端] 删除用户失败:",a),{success:!1,message:a.response?.data?.message||"删除用户失败"}}finally{e("SET_LOADING",!1)}},async fetchWorkerSalaryConfig({commit:e},t){try{console.log("[前端] 获取工人薪资配置...",t);const e=await m.A.get(`/api/salary/config/${t}`);return console.log("[前端] 获取薪资配置成功:",e),{success:!0,data:e.data||e}}catch(s){return console.error("[前端] 获取薪资配置失败:",s),{success:!1,message:s.response?.data?.message||"获取薪资配置失败"}}},async fetchWorkerStats({commit:e}){try{console.log("[前端] 获取工人统计数据...");const e=await m.A.get("/api/worker/stats");return console.log("[前端] 获取工人统计数据成功:",e),{success:!0,data:e.data||e}}catch(t){return console.error("[前端] 获取工人统计数据失败:",t),{success:!1,message:t.response?.data?.message||"获取工人统计数据失败"}}},async fetchOrderStatusStats({commit:e}){try{console.log("[前端] 获取工单状态统计...");const e=await m.A.get("/api/admin/stats/order-status");return console.log("[前端] 获取工单状态统计成功:",e),{success:!0,data:e.data||e}}catch(t){return console.error("[前端] 获取工单状态统计失败:",t),{success:!1,message:t.response?.data?.message||"获取工单状态统计失败"}}},async fetchApplianceTypeStats({commit:e}){try{console.log("[前端] 获取设备类型统计...");const e=await m.A.get("/api/admin/stats/appliance-type");return console.log("[前端] 获取设备类型统计成功:",e),{success:!0,data:e.data||e}}catch(t){return console.error("[前端] 获取设备类型统计失败:",t),{success:!1,message:t.response?.data?.message||"获取设备类型统计失败"}}},async fetchWorkerPerformanceStats({commit:e}){try{console.log("[前端] 获取工人绩效统计...");const e=await m.A.get("/api/admin/stats/worker-performance");return console.log("[前端] 获取工人绩效统计成功:",e),{success:!0,data:e.data||e}}catch(t){return console.error("[前端] 获取工人绩效统计失败:",t),{success:!1,message:t.response?.data?.message||"获取工人绩效统计失败"}}},async fetchMonthlyTrendStats({commit:e}){try{console.log("[前端] 获取月度趋势统计...");const e=await m.A.get("/api/admin/stats/monthly-trend");return console.log("[前端] 获取月度趋势统计成功:",e),{success:!0,data:e.data||e}}catch(t){return console.error("[前端] 获取月度趋势统计失败:",t),{success:!1,message:t.response?.data?.message||"获取月度趋势统计失败"}}},async fetchAvailableOrders({commit:e}){try{e("SET_LOADING",!0),console.log("[前端] 获取可接工单列表...");const t=await m.A.get("/api/worker/available-orders");return console.log("[前端] 获取可接工单成功:",t.data),e("SET_AVAILABLE_ORDERS",t.data.data||t.data),{success:!0}}catch(t){return console.error("[前端] 获取可接工单失败:",t),{success:!1,message:t.response?.data?.message||"获取可接工单失败"}}finally{e("SET_LOADING",!1)}},async acceptOrder({commit:e},t){try{e("SET_LOADING",!0),console.log("[前端] 接单请求:",t);const s=await m.A.post(`/api/worker/accept-order/${t}`);return console.log("[前端] 接单成功:",s),{success:!0,data:s.data||s}}catch(s){return console.error("[前端] 接单失败:",s),{success:!1,message:s.response?.data?.message||"接单失败"}}finally{e("SET_LOADING",!1)}},async startRepair({commit:e},t){try{e("SET_LOADING",!0),console.log("[前端] 开始维修请求:",t);const s=await m.A.post(`/api/worker/start-repair/${t}`);return console.log("[前端] 开始维修成功:",s),{success:!0,data:s.data||s}}catch(s){return console.error("[前端] 开始维修失败:",s),{success:!1,message:s.response?.data?.message||"开始维修失败"}}finally{e("SET_LOADING",!1)}},async updateProgress({commit:e},{orderId:t,progressData:s}){try{e("SET_LOADING",!0),console.log("[前端] 更新进度请求:",t,s);const a=await m.A.post(`/api/worker/update-progress/${t}`,s);return console.log("[前端] 更新进度成功:",a),{success:!0,data:a.data||a}}catch(a){return console.error("[前端] 更新进度失败:",a),{success:!1,message:a.response?.data?.message||"更新进度失败"}}finally{e("SET_LOADING",!1)}},async completeRepair({commit:e},{orderId:t,repairData:s}){try{e("SET_LOADING",!0),console.log("[前端] 完成维修请求:",t,s);const a=await m.A.post(`/api/worker/complete-repair/${t}`,s);return console.log("[前端] 完成维修成功:",a),{success:!0,data:a.data||a}}catch(a){return console.error("[前端] 完成维修失败:",a),{success:!1,message:a.response?.data?.message||"完成维修失败"}}finally{e("SET_LOADING",!1)}}}}),g=function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-container customer-theme"},[t("div",{staticClass:"login-form"},[t("div",{staticClass:"login-header"},[t("div",{staticClass:"back-btn"},[t("el-button",{attrs:{icon:"el-icon-arrow-left"},on:{click:function(t){return e.$router.push("/")}}},[e._v("返回首页")])],1),t("h2",[e._v("客户登录")]),t("p",[e._v("永盛制冷维修有限公司-家电售卖与维修")])]),t("el-form",{ref:"loginForm",attrs:{model:e.loginForm,rules:e.loginRules,"label-width":"0"},nativeOn:{submit:function(t){return t.preventDefault(),e.handleLogin.apply(null,arguments)}}},[t("el-form-item",{attrs:{prop:"username"}},[t("el-input",{attrs:{placeholder:"请输入用户名","prefix-icon":"el-icon-user",size:"large"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}})],1),t("el-form-item",{attrs:{prop:"password"}},[t("el-input",{attrs:{type:"password",placeholder:"请输入密码","prefix-icon":"el-icon-lock",size:"large","show-password":""},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}})],1),t("el-form-item",[t("el-button",{staticStyle:{width:"100%"},attrs:{type:"primary",size:"large",loading:e.$store.state.loading},on:{click:e.handleLogin}},[e._v(" 登录 ")])],1)],1),t("div",{staticClass:"login-footer"},[t("p",[e._v(" 还没有账号？ "),t("router-link",{attrs:{to:"/customer/register"}},[e._v("立即注册")])],1)])],1)])},v=[],_={name:"CustomerLogin",data(){return{loginForm:{username:"",password:""},loginRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]}}},methods:{async handleLogin(){try{await this.$refs.loginForm.validate();const e=await this.$store.dispatch("login",{credentials:this.loginForm,userType:"customer"});e.success?(this.$message.success("登录成功"),this.$router.push("/customer/home")):this.$message.error(e.message)}catch(e){console.error("登录失败:",e)}}}},f=_,b=(0,n.A)(f,g,v,!1,null,"9dad0244",null),y=b.exports,w=function(){var e=this,t=e._self._c;return t("div",{staticClass:"register-container customer-theme"},[t("div",{staticClass:"register-form"},[t("div",{staticClass:"register-header"},[t("div",{staticClass:"back-btn"},[t("el-button",{attrs:{icon:"el-icon-arrow-left"},on:{click:function(t){return e.$router.push("/")}}},[e._v("返回首页")])],1),t("h2",[e._v("客户注册")]),t("p",[e._v("创建您的账户")])]),t("el-form",{ref:"registerForm",attrs:{model:e.registerForm,rules:e.registerRules,"label-width":"80px"},nativeOn:{submit:function(t){return t.preventDefault(),e.handleRegister.apply(null,arguments)}}},[t("el-form-item",{attrs:{label:"用户名",prop:"username"}},[t("el-input",{attrs:{placeholder:"请输入用户名"},model:{value:e.registerForm.username,callback:function(t){e.$set(e.registerForm,"username",t)},expression:"registerForm.username"}})],1),t("el-form-item",{attrs:{label:"密码",prop:"password"}},[t("el-input",{attrs:{type:"password",placeholder:"请输入密码","show-password":""},model:{value:e.registerForm.password,callback:function(t){e.$set(e.registerForm,"password",t)},expression:"registerForm.password"}})],1),t("el-form-item",{attrs:{label:"确认密码",prop:"confirmPassword"}},[t("el-input",{attrs:{type:"password",placeholder:"请再次输入密码","show-password":""},model:{value:e.registerForm.confirmPassword,callback:function(t){e.$set(e.registerForm,"confirmPassword",t)},expression:"registerForm.confirmPassword"}})],1),t("el-form-item",{attrs:{label:"姓名",prop:"name"}},[t("el-input",{attrs:{placeholder:"请输入真实姓名"},model:{value:e.registerForm.name,callback:function(t){e.$set(e.registerForm,"name",t)},expression:"registerForm.name"}})],1),t("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[t("el-input",{attrs:{placeholder:"请输入手机号"},model:{value:e.registerForm.phone,callback:function(t){e.$set(e.registerForm,"phone",t)},expression:"registerForm.phone"}})],1),t("el-form-item",{attrs:{label:"地址",prop:"address"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入详细地址",rows:3},model:{value:e.registerForm.address,callback:function(t){e.$set(e.registerForm,"address",t)},expression:"registerForm.address"}})],1),t("el-form-item",[t("el-button",{staticStyle:{width:"100%"},attrs:{type:"primary",loading:e.$store.state.loading},on:{click:e.handleRegister}},[e._v(" 注册 ")])],1)],1),t("div",{staticClass:"register-footer"},[t("p",[e._v(" 已有账号？ "),t("router-link",{attrs:{to:"/customer/login"}},[e._v("立即登录")])],1)])],1)])},C=[],S={name:"CustomerRegister",data(){const e=(e,t,s)=>{t!==this.registerForm.password?s(new Error("两次输入密码不一致")):s()};return{registerForm:{username:"",password:"",confirmPassword:"",name:"",phone:"",address:""},registerRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,message:"用户名长度不能少于3位",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:e,trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}]}}},methods:{async handleRegister(){try{await this.$refs.registerForm.validate();const{confirmPassword:e,...t}=this.registerForm;console.log("[注册页面] 发送注册请求:",t);const s=await this.$store.dispatch("register",t);console.log("[注册页面] 注册结果:",s),s&&s.success?(this.$message.success("注册成功，请登录"),console.log("[注册页面] 准备跳转到登录页面"),this.$router.push("/customer/login")):(this.$message.error(s?.message||"注册失败"),console.log("[注册页面] 注册失败:",s))}catch(e){console.error("[注册页面] 注册异常:",e),this.$message.error("注册失败，请重试")}}}},k=S,T=(0,n.A)(k,w,C,!1,null,"27cdf9d8",null),$=T.exports,O=function(){var e=this,t=e._self._c;return t("div",{staticClass:"customer-home"},[t("el-container",[t("el-header",{staticClass:"header"},[t("div",{staticClass:"header-left"},[t("h2",[e._v("客户端 - 永盛制冷维修有限公司")])]),t("div",{staticClass:"header-right"},[t("el-dropdown",{on:{command:e.handleCommand}},[t("span",{staticClass:"user-info"},[t("i",{staticClass:"el-icon-user"}),e._v(" "+e._s(e.currentUser.name)+" "),t("i",{staticClass:"el-icon-arrow-down"})]),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{attrs:{command:"logout",divided:""}},[e._v("退出登录")])],1)],1)],1)]),t("el-main",{staticClass:"main-content"},[t("div",{staticClass:"welcome-section"},[t("div",{staticClass:"welcome-content"},[t("h1",[e._v("欢迎回来，"+e._s(e.currentUser.name)+"！")]),t("p",[e._v("您可以在这里提交维修申请和查看工单状态")]),t("div",{staticClass:"welcome-decoration"})])]),t("el-row",{staticClass:"stats-row",attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-icon pending"},[t("i",{staticClass:"el-icon-document"})]),t("div",{staticClass:"stat-info"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.customerStats.totalOrders))]),t("div",{staticClass:"stat-label"},[e._v("总工单数")])])])])],1),t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-icon processing"},[t("i",{staticClass:"el-icon-loading"})]),t("div",{staticClass:"stat-info"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.customerStats.inProgress))]),t("div",{staticClass:"stat-label"},[e._v("进行中")])])])])],1),t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-icon completed"},[t("i",{staticClass:"el-icon-circle-check"})]),t("div",{staticClass:"stat-info"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.customerStats.completed))]),t("div",{staticClass:"stat-label"},[e._v("已完成")])])])])],1)],1),t("div",{staticClass:"quick-actions"},[t("h2",[e._v("快捷操作")]),t("el-row",{staticClass:"action-row",attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"action-card",attrs:{shadow:"hover"},nativeOn:{click:function(t){return e.$safeRouter.push("/customer/create-order")}}},[t("div",{staticClass:"card-content"},[t("div",{staticClass:"action-icon"},[t("i",{staticClass:"el-icon-plus"})]),t("h3",[e._v("创建工单")]),t("p",[e._v("提交新的维修申请")])])])],1),t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"action-card",attrs:{shadow:"hover"},nativeOn:{click:function(t){return e.$safeRouter.push("/customer/orders")}}},[t("div",{staticClass:"card-content"},[t("div",{staticClass:"action-icon"},[t("i",{staticClass:"el-icon-document"})]),t("h3",[e._v("我的工单")]),t("p",[e._v("查看工单状态和历史")])])])],1),t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"action-card contact-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"card-content"},[t("div",{staticClass:"action-icon"},[t("i",{staticClass:"el-icon-phone"})]),t("h3",[e._v("联系客服")]),t("p",[e._v("📞 13471579359")])])])],1)],1)],1),e.recentOrders.length>0?t("div",{staticClass:"recent-orders"},[t("h2",[e._v("最近的工单")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.recentOrders}},[t("el-table-column",{attrs:{prop:"id",label:"工单号",width:"100"}}),t("el-table-column",{attrs:{prop:"appliance_type",label:"家电类型",width:"120"}}),t("el-table-column",{attrs:{prop:"problem_description",label:"问题描述","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusType(s.row.status)}},[e._v(" "+e._s(e.getStatusText(s.row.status))+" ")])]}}],null,!1,2263538733)}),t("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDate(t.row.created_at))+" ")]}}],null,!1,3570427137)})],1)],1):e._e()],1)],1),t("div",{staticClass:"floating-wechat",on:{click:e.toggleQRCode}},[t("div",{staticClass:"wechat-label"},[e._v("微信二维码")]),e._m(0),t("div",{directives:[{name:"show",rawName:"v-show",value:e.showQRCode,expression:"showQRCode"}],staticClass:"qr-popup",on:{click:function(e){e.stopPropagation()}}},[t("div",{staticClass:"qr-header"},[t("span",[e._v("扫码添加微信")]),t("i",{staticClass:"el-icon-close",on:{click:function(t){e.showQRCode=!1}}})]),e._m(1)])])],1)},x=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"wechat-icon"},[t("i",{staticClass:"el-icon-chat-dot-round"})])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"qr-content"},[t("p",{staticClass:"qr-title"},[e._v("微信二维码")]),t("img",{staticClass:"floating-qr-image",attrs:{src:"/images/微信二维码.jpg",alt:"微信二维码 A13471579359"}}),t("p",{staticClass:"wechat-id"},[e._v("微信号：A13471579359")]),t("p",{staticClass:"qr-tip"},[e._v("长按保存二维码，微信扫码添加")])])}],F={name:"CustomerHome",data(){return{showQRCode:!1}},computed:{...(0,p.L8)(["currentUser"]),recentOrders(){return this.$store.state.orders.slice(0,5)},customerStats(){const e=this.$store.state.orders||[];return{totalOrders:e.length,inProgress:e.filter(e=>["pending","accepted","in_progress"].includes(e.status)).length,completed:e.filter(e=>"completed"===e.status).length}}},async created(){await this.$store.dispatch("fetchCustomerOrders")},mounted(){document.addEventListener("click",this.handleClickOutside)},beforeDestroy(){document.removeEventListener("click",this.handleClickOutside)},methods:{toggleQRCode(){this.showQRCode=!this.showQRCode},handleClickOutside(e){const t=document.querySelector(".floating-wechat");t&&!t.contains(e.target)&&(this.showQRCode=!1)},async handleCommand(e){if("logout"===e)try{await this.$confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await this.$store.dispatch("logout");e.success&&(this.$message.success("退出登录成功"),this.$safeRouter.push("/customer/login"))}catch(t){console.log("用户取消退出登录")}},getStatusType(e){const t={pending:"warning",accepted:"info",in_progress:"primary",completed:"success",cancelled:"danger"};return t[e]||"info"},getStatusText(e){const t={pending:"待接单",accepted:"已接单",in_progress:"维修中",completed:"已完成",cancelled:"已取消"};return t[e]||"未知状态"},formatDate(e){if(!e)return"-";const t=new Date(e);return t.toLocaleString("zh-CN")}}},A=F,D=(0,n.A)(A,O,x,!1,null,"b59b0276",null),U=D.exports,R=function(){var e=this,t=e._self._c;return t("div",{staticClass:"customer-orders"},[t("el-container",[t("el-header",{staticClass:"header"},[t("div",{staticClass:"header-left"},[t("el-button",{attrs:{icon:"el-icon-arrow-left"},on:{click:function(t){return e.$safeRouter.push("/customer/home")}}},[e._v("返回首页")]),t("h2",[e._v("我的工单")])],1),t("div",{staticClass:"header-right"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.$safeRouter.push("/customer/create-order")}}},[e._v(" 创建工单 ")])],1)]),t("el-main",{staticClass:"main-content"},[t("el-card",{staticClass:"filter-card",attrs:{shadow:"never"}},[t("el-row",{attrs:{gutter:20,type:"flex",align:"middle"}},[t("el-col",{attrs:{span:6}},[t("el-select",{attrs:{placeholder:"工单状态",clearable:""},on:{change:e.handleFilter},model:{value:e.filterStatus,callback:function(t){e.filterStatus=t},expression:"filterStatus"}},[t("el-option",{attrs:{label:"全部状态",value:""}}),t("el-option",{attrs:{label:"待接单",value:"pending"}}),t("el-option",{attrs:{label:"已接单",value:"accepted"}}),t("el-option",{attrs:{label:"维修中",value:"in_progress"}}),t("el-option",{attrs:{label:"已完成",value:"completed"}}),t("el-option",{attrs:{label:"已取消",value:"cancelled"}})],1)],1),t("el-col",{attrs:{span:6}},[t("el-select",{attrs:{placeholder:"家电类型",clearable:""},on:{change:e.handleFilter},model:{value:e.filterType,callback:function(t){e.filterType=t},expression:"filterType"}},[t("el-option",{attrs:{label:"全部类型",value:""}}),t("el-option",{attrs:{label:"洗衣机",value:"washing_machine"}}),t("el-option",{attrs:{label:"冰箱",value:"refrigerator"}}),t("el-option",{attrs:{label:"空调",value:"air_conditioner"}}),t("el-option",{attrs:{label:"电视",value:"television"}}),t("el-option",{attrs:{label:"微波炉",value:"microwave"}}),t("el-option",{attrs:{label:"热水器",value:"water_heater"}}),t("el-option",{attrs:{label:"油烟机",value:"range_hood"}}),t("el-option",{attrs:{label:"燃气灶",value:"gas_stove"}}),t("el-option",{attrs:{label:"其他",value:"other"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-input",{attrs:{placeholder:"搜索工单号、品牌型号或问题描述","prefix-icon":"el-icon-search",clearable:""},on:{input:e.handleSearch},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}})],1),t("el-col",{attrs:{span:4}},[t("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.refreshOrders}},[e._v("刷新")])],1)],1)],1),t("el-row",{staticClass:"stats-row",attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stat-card"},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.orderStats.total))]),t("div",{staticClass:"stat-label"},[e._v("总工单数")])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stat-card"},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number pending"},[e._v(e._s(e.orderStats.pending))]),t("div",{staticClass:"stat-label"},[e._v("待接单")])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stat-card"},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number processing"},[e._v(e._s(e.orderStats.processing))]),t("div",{staticClass:"stat-label"},[e._v("处理中")])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stat-card"},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number completed"},[e._v(e._s(e.orderStats.completed))]),t("div",{staticClass:"stat-label"},[e._v("已完成")])])])],1)],1),t("el-card",{staticClass:"orders-card"},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("工单列表")]),t("small",[e._v("共 "+e._s(e.filteredOrders.length)+" 条记录")])]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"desktop-table",staticStyle:{width:"100%"},attrs:{data:e.paginatedOrders,"row-class-name":"order-row"},on:{"row-click":e.viewOrderDetail}},[t("el-table-column",{attrs:{prop:"id",label:"工单号",width:"100",fixed:"left"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("span",{staticClass:"order-id"},[e._v("#"+e._s(s.row.id))])]}}])}),t("el-table-column",{attrs:{prop:"appliance_type",label:"家电类型",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getApplianceTypeName(t.row.appliance_type))+" ")]}}])}),t("el-table-column",{attrs:{prop:"brand_model",label:"品牌型号",width:"150","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"problem_description",label:"问题描述","min-width":"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusType(s.row.status),size:"small"}},[e._v(" "+e._s(e.getStatusText(s.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"urgency",label:"紧急程度",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getUrgencyType(s.row.urgency),size:"small"}},[e._v(" "+e._s(e.getUrgencyText(s.row.urgency))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"worker_name",label:"维修师傅",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.worker_name||"未分配")+" ")]}}])}),t("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDate(t.row.created_at))+" ")]}}])}),t("el-table-column",{attrs:{label:"操作",width:"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{size:"mini"},on:{click:function(t){return t.stopPropagation(),e.viewOrderDetail(s.row)}}},[e._v("查看")]),"pending"===s.row.status?t("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return t.stopPropagation(),e.cancelOrder(s.row)}}},[e._v(" 取消 ")]):e._e()]}}])})],1),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"mobile-order-list"},e._l(e.paginatedOrders,function(s){return t("MobileOrderCard",{key:s.id,attrs:{order:s},on:{"view-detail":e.viewOrderDetail},scopedSlots:e._u([{key:"actions",fn:function({order:s}){return[t("el-button",{attrs:{size:"mini"},on:{click:function(t){return t.stopPropagation(),e.viewOrderDetail(s)}}},[e._v("查看")]),"pending"===s.status?t("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return t.stopPropagation(),e.cancelOrder(s)}}},[e._v(" 取消 ")]):e._e()]}}],null,!0)})}),1),t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.filteredOrders.length},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)],1),t("el-dialog",{attrs:{visible:e.detailVisible,title:"工单详情",width:"800px","before-close":e.handleDetailClose},on:{"update:visible":function(t){e.detailVisible=t}}},[e.selectedOrder?t("order-detail",{attrs:{order:e.selectedOrder},on:{refresh:e.refreshOrders}}):e._e()],1)],1)},z=[],P=function(){var e=this,t=e._self._c;return t("div",{staticClass:"order-detail"},[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"工单号"}},[t("span",{staticClass:"order-id"},[e._v("#"+e._s(e.order.id))])]),t("el-descriptions-item",{attrs:{label:"状态"}},[t("el-tag",{attrs:{type:e.getStatusType(e.order.status)}},[e._v(" "+e._s(e.getStatusText(e.order.status))+" ")])],1),t("el-descriptions-item",{attrs:{label:"家电类型"}},[e._v(" "+e._s(e.getApplianceTypeName(e.order.appliance_type))+" ")]),t("el-descriptions-item",{attrs:{label:"品牌型号"}},[e._v(" "+e._s(e.order.brand_model)+" ")]),t("el-descriptions-item",{attrs:{label:"紧急程度"}},[t("el-tag",{attrs:{type:e.getUrgencyType(e.order.urgency),size:"small"}},[e._v(" "+e._s(e.getUrgencyText(e.order.urgency))+" ")])],1),t("el-descriptions-item",{attrs:{label:"购买时间"}},[e._v(" "+e._s(e.formatDate(e.order.purchase_date))+" ")]),t("el-descriptions-item",{attrs:{label:"联系人"}},[e._v(" "+e._s(e.order.contact_name)+" ")]),t("el-descriptions-item",{attrs:{label:"联系电话"}},[e._v(" "+e._s(e.order.contact_phone)+" ")]),e.order.worker_name?t("el-descriptions-item",{attrs:{label:"维修师傅"}},[e._v(" "+e._s(e.order.worker_name)+" ")]):e._e(),e.order.worker_phone?t("el-descriptions-item",{attrs:{label:"师傅电话"}},[e._v(" "+e._s(e.order.worker_phone)+" ")]):e._e(),t("el-descriptions-item",{attrs:{label:"创建时间"}},[e._v(" "+e._s(e.formatDate(e.order.created_at))+" ")]),t("el-descriptions-item",{attrs:{label:"预约时间"}},[e._v(" "+e._s(e.formatDate(e.order.preferred_date))+" "+e._s(e.getTimeSlotText(e.order.preferred_time))+" ")])],1),t("el-divider",{attrs:{"content-position":"left"}},[e._v("详细地址")]),t("p",{staticClass:"address"},[e._v(e._s(e.order.address))]),t("el-divider",{attrs:{"content-position":"left"}},[e._v("故障描述")]),t("p",{staticClass:"description"},[e._v(e._s(e.order.problem_description))]),e.order.remarks?t("el-divider",{attrs:{"content-position":"left"}},[e._v("备注信息")]):e._e(),e.order.remarks?t("p",{staticClass:"remarks"},[e._v(e._s(e.order.remarks))]):e._e(),e.order.images&&e.order.images.length>0?t("el-divider",{attrs:{"content-position":"left"}},[e._v("故障图片")]):e._e(),e.order.images&&e.order.images.length>0?t("div",{staticClass:"images"},e._l(e.order.images,function(s,a){return t("el-image",{key:a,staticClass:"image-item",attrs:{src:s,"preview-src-list":e.order.images,fit:"cover"}})}),1):e._e(),e.order.progress&&e.order.progress.length>0?t("el-divider",{attrs:{"content-position":"left"}},[e._v("维修进度")]):e._e(),e.order.progress&&e.order.progress.length>0?t("el-timeline",e._l(e.order.progress,function(s,a){return t("el-timeline-item",{key:a,attrs:{timestamp:e.formatDate(s.created_at),placement:"top"}},[t("el-card",[t("h4",[e._v(e._s(s.title))]),t("p",[e._v(e._s(s.description))]),s.images&&s.images.length>0?t("div",{staticClass:"progress-images"},e._l(s.images,function(e,a){return t("el-image",{key:a,staticClass:"progress-image",attrs:{src:e,"preview-src-list":s.images,fit:"cover"}})}),1):e._e()])],1)}),1):e._e(),"pending"===e.order.status?t("div",{staticClass:"actions"},[t("el-button",{attrs:{type:"danger"},on:{click:e.cancelOrder}},[e._v("取消工单")])],1):e._e(),"completed"===e.order.status?t("div",{staticClass:"actions"},[t("el-rate",{attrs:{disabled:e.order.rating>0,"show-text":"",texts:["极差","失望","一般","满意","惊喜"]},model:{value:e.rating,callback:function(t){e.rating=t},expression:"rating"}}),e.order.rating?e._e():t("el-input",{staticStyle:{"margin-top":"10px"},attrs:{type:"textarea",placeholder:"请对本次服务进行评价",rows:3,maxlength:"200","show-word-limit":""},model:{value:e.comment,callback:function(t){e.comment=t},expression:"comment"}}),e.order.rating?e._e():t("el-button",{staticStyle:{"margin-top":"10px"},attrs:{type:"primary"},on:{click:e.submitRating}},[e._v(" 提交评价 ")]),e.order.rating?t("div",{staticClass:"existing-rating"},[e._m(0),t("el-rate",{attrs:{value:e.order.rating,disabled:"","show-score":""}}),e.order.comment?t("p",[e._v(e._s(e.order.comment))]):e._e()],1):e._e()],1):e._e()],1)},L=[function(){var e=this,t=e._self._c;return t("p",[t("strong",[e._v("您的评价：")])])}],M={name:"OrderDetail",props:{order:{type:Object,required:!0}},data(){return{rating:0,comment:""}},methods:{async cancelOrder(){try{await this.$confirm("确定要取消这个工单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),this.$message.success("工单已取消"),this.$emit("refresh")}catch(e){"cancel"!==e&&this.$message.error("取消工单失败")}},async submitRating(){if(0!==this.rating)try{this.$message.success("评价提交成功"),this.$emit("refresh")}catch(e){this.$message.error("评价提交失败")}else this.$message.warning("请选择评分")},getApplianceTypeName(e){const t={washing_machine:"洗衣机",refrigerator:"冰箱",air_conditioner:"空调",television:"电视",microwave:"微波炉",water_heater:"热水器",range_hood:"油烟机",gas_stove:"燃气灶",other:"其他"};return t[e]||e},getStatusType(e){const t={pending:"warning",accepted:"info",in_progress:"primary",completed:"success",cancelled:"danger"};return t[e]||"info"},getStatusText(e){const t={pending:"待接单",accepted:"已接单",in_progress:"维修中",completed:"已完成",cancelled:"已取消"};return t[e]||"未知状态"},getUrgencyType(e){const t={low:"info",medium:"warning",high:"danger"};return t[e]||"info"},getUrgencyText(e){const t={low:"一般",medium:"紧急",high:"非常紧急"};return t[e]||"一般"},getTimeSlotText(e){const t={morning:"上午 (9:00-12:00)",afternoon:"下午 (13:00-17:00)",evening:"晚上 (18:00-20:00)"};return t[e]||""},formatDate(e){if(!e)return"-";const t=new Date(e);return t.toLocaleString("zh-CN")}}},E=M,I=(0,n.A)(E,P,L,!1,null,"07d3b7f8",null),N=I.exports,q=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mobile-order-card",on:{click:function(t){return e.$emit("view-detail",e.order)}}},[t("div",{staticClass:"card-header"},[t("div",{staticClass:"order-info"},[t("span",{staticClass:"order-id"},[e._v("#"+e._s(e.order.id))]),t("el-tag",{attrs:{type:e.getStatusType(e.order.status),size:"mini"}},[e._v(" "+e._s(e.getStatusText(e.order.status))+" ")])],1),t("div",{staticClass:"urgency-badge",class:e.order.urgency},[e._v(" "+e._s(e.getUrgencyText(e.order.urgency))+" ")])]),t("div",{staticClass:"card-content"},[t("div",{staticClass:"appliance-info"},[t("i",{staticClass:"el-icon-cpu"}),t("span",{staticClass:"appliance-type"},[e._v(e._s(e.getApplianceTypeName(e.order.appliance_type)))]),t("span",{staticClass:"brand-model"},[e._v(e._s(e.order.brand_model))])]),t("div",{staticClass:"problem-desc"},[t("i",{staticClass:"el-icon-warning-outline"}),t("span",[e._v(e._s(e.order.problem_description))])]),t("div",{staticClass:"contact-info"},[t("i",{staticClass:"el-icon-user"}),t("span",[e._v(e._s(e.order.contact_name))]),t("i",{staticClass:"el-icon-phone"}),t("span",[e._v(e._s(e.order.contact_phone))])]),t("div",{staticClass:"address-info"},[t("i",{staticClass:"el-icon-location-outline"}),t("span",[e._v(e._s(e.order.address))])]),t("div",{staticClass:"time-info"},[t("div",{staticClass:"create-time"},[t("i",{staticClass:"el-icon-time"}),t("span",[e._v("创建："+e._s(e.formatDate(e.order.created_at)))])]),e.order.preferred_date?t("div",{staticClass:"preferred-time"},[t("i",{staticClass:"el-icon-date"}),t("span",[e._v("预约："+e._s(e.order.preferred_date)+" "+e._s(e.getTimeSlotText(e.order.preferred_time)))])]):e._e()])]),e.showActions?t("div",{staticClass:"card-actions"},[e._t("actions",null,{order:e.order})],2):e._e()])},V=[],G={name:"MobileOrderCard",props:{order:{type:Object,required:!0},showActions:{type:Boolean,default:!0}},methods:{getStatusText(e){const t={pending:"待接单",accepted:"已接单",in_progress:"维修中",completed:"已完成",cancelled:"已取消"};return t[e]||e},getStatusType(e){const t={pending:"info",accepted:"warning",in_progress:"primary",completed:"success",cancelled:"danger"};return t[e]||"info"},getUrgencyText(e){const t={low:"一般",medium:"紧急",high:"非常紧急"};return t[e]||e},getApplianceTypeName(e){const t={air_conditioner:"空调",washing_machine:"洗衣机",refrigerator:"冰箱",water_heater:"热水器",television:"电视",microwave:"微波炉"};return t[e]||e},getTimeSlotText(e){const t={morning:"上午",afternoon:"下午",evening:"晚上"};return t[e]||e},formatDate(e){if(!e)return"-";const t=new Date(e);return t.toLocaleDateString("zh-CN")}}},B=G,j=(0,n.A)(B,q,V,!1,null,"2425f2bf",null),W=j.exports,K={name:"CustomerOrders",components:{OrderDetail:N,MobileOrderCard:W},computed:{...(0,p.L8)(["currentUser"]),orders(){return this.$store.state.orders||[]},filteredOrders(){let e=this.orders;if(this.filterStatus&&(e=e.filter(e=>e.status===this.filterStatus)),this.filterType&&(e=e.filter(e=>e.appliance_type===this.filterType)),this.searchKeyword){const t=this.searchKeyword.toLowerCase();e=e.filter(e=>e.id.toString().includes(t)||e.brand_model.toLowerCase().includes(t)||e.problem_description.toLowerCase().includes(t))}return e},paginatedOrders(){const e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;return this.filteredOrders.slice(e,t)},orderStats(){const e={total:this.orders.length,pending:0,processing:0,completed:0};return this.orders.forEach(t=>{switch(t.status){case"pending":e.pending++;break;case"accepted":case"in_progress":e.processing++;break;case"completed":e.completed++;break}}),e}},data(){return{loading:!1,detailVisible:!1,selectedOrder:null,filterStatus:"",filterType:"",searchKeyword:"",currentPage:1,pageSize:10}},async created(){await this.refreshOrders()},methods:{async refreshOrders(){this.loading=!0;try{await this.$store.dispatch("fetchCustomerOrders")}catch(e){console.error("[前端] 获取工单列表失败:",e),this.$message.error("获取工单列表失败")}finally{this.loading=!1}},handleFilter(){this.currentPage=1},handleSearch(){this.currentPage=1},handleSizeChange(e){this.pageSize=e,this.currentPage=1},handleCurrentChange(e){this.currentPage=e},viewOrderDetail(e){this.selectedOrder=e,this.detailVisible=!0},handleDetailClose(){this.detailVisible=!1,this.selectedOrder=null},async cancelOrder(e){try{await this.$confirm("确定要取消这个工单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),this.$message.success("工单已取消"),await this.refreshOrders()}catch(t){"cancel"!==t&&this.$message.error("取消工单失败")}},getApplianceTypeName(e){const t={washing_machine:"洗衣机",refrigerator:"冰箱",air_conditioner:"空调",television:"电视",microwave:"微波炉",water_heater:"热水器",range_hood:"油烟机",gas_stove:"燃气灶",other:"其他"};return t[e]||e},getStatusType(e){const t={pending:"warning",accepted:"info",in_progress:"primary",completed:"success",cancelled:"danger"};return t[e]||"info"},getStatusText(e){const t={pending:"待接单",accepted:"已接单",in_progress:"维修中",completed:"已完成",cancelled:"已取消"};return t[e]||"未知状态"},getUrgencyType(e){const t={low:"info",medium:"warning",high:"danger"};return t[e]||"info"},getUrgencyText(e){const t={low:"一般",medium:"紧急",high:"非常紧急"};return t[e]||"一般"},formatDate(e){if(!e)return"-";const t=new Date(e);return t.toLocaleString("zh-CN")}}},H=K,Q=(0,n.A)(H,R,z,!1,null,"81386508",null),J=Q.exports,Y=function(){var e=this,t=e._self._c;return t("div",{staticClass:"create-order"},[t("el-container",[t("el-header",{staticClass:"header"},[t("div",{staticClass:"header-left"},[t("el-button",{attrs:{icon:"el-icon-arrow-left"},on:{click:function(t){return e.$safeRouter.push("/customer/home")}}},[e._v("返回首页")]),t("h2",[e._v("创建维修工单")])],1),t("div",{staticClass:"header-right"},[t("span",{staticClass:"user-info"},[t("i",{staticClass:"el-icon-user"}),e._v(" "+e._s(e.currentUser.name)+" ")])])]),t("el-main",{staticClass:"main-content"},[t("el-card",{staticClass:"form-card"},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("快速下单")]),t("small",[e._v("简单几步，一句话说清楚哪里坏了就可以下单")])]),t("el-form",{ref:"orderForm",attrs:{model:e.orderForm,rules:e.orderRules,"label-width":"120px"},nativeOn:{submit:function(t){return t.preventDefault(),e.submitOrder.apply(null,arguments)}}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"家电类型",prop:"appliance_type"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择家电类型"},model:{value:e.orderForm.appliance_type,callback:function(t){e.$set(e.orderForm,"appliance_type",t)},expression:"orderForm.appliance_type"}},[t("el-option",{attrs:{label:"洗衣机",value:"washing_machine"}}),t("el-option",{attrs:{label:"冰箱",value:"refrigerator"}}),t("el-option",{attrs:{label:"空调",value:"air_conditioner"}}),t("el-option",{attrs:{label:"电视",value:"television"}}),t("el-option",{attrs:{label:"微波炉",value:"microwave"}}),t("el-option",{attrs:{label:"热水器",value:"water_heater"}}),t("el-option",{attrs:{label:"油烟机",value:"range_hood"}}),t("el-option",{attrs:{label:"燃气灶",value:"gas_stove"}}),t("el-option",{attrs:{label:"其他",value:"other"}})],1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"品牌型号（可选）"}},[t("el-input",{attrs:{placeholder:"如果知道品牌型号可以填写，如：海尔XQG80-B12866"},model:{value:e.orderForm.brand_model,callback:function(t){e.$set(e.orderForm,"brand_model",t)},expression:"orderForm.brand_model"}})],1)],1)],1),t("el-form-item",{attrs:{label:"紧急程度",prop:"urgency"}},[t("el-radio-group",{model:{value:e.orderForm.urgency,callback:function(t){e.$set(e.orderForm,"urgency",t)},expression:"orderForm.urgency"}},[t("el-radio",{attrs:{label:"low"}},[e._v("一般")]),t("el-radio",{attrs:{label:"medium"}},[e._v("紧急")]),t("el-radio",{attrs:{label:"high"}},[e._v("非常紧急")])],1)],1),t("el-form-item",{attrs:{label:"故障描述",prop:"problem_description"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"一句话说清楚哪里坏了就可以，如：洗衣机不转了、空调不制冷、冰箱不冷冻...",maxlength:"200","show-word-limit":""},model:{value:e.orderForm.problem_description,callback:function(t){e.$set(e.orderForm,"problem_description",t)},expression:"orderForm.problem_description"}})],1),t("el-divider",{attrs:{"content-position":"left"}},[e._v("联系信息")]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"联系人",prop:"contact_name"}},[t("el-input",{attrs:{placeholder:"请输入联系人姓名"},model:{value:e.orderForm.contact_name,callback:function(t){e.$set(e.orderForm,"contact_name",t)},expression:"orderForm.contact_name"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"联系电话",prop:"contact_phone"}},[t("el-input",{attrs:{placeholder:"请输入手机号码"},model:{value:e.orderForm.contact_phone,callback:function(t){e.$set(e.orderForm,"contact_phone",t)},expression:"orderForm.contact_phone"}})],1)],1)],1),t("el-form-item",{attrs:{label:"详细地址",prop:"address"}},[t("el-input",{attrs:{placeholder:"请输入详细地址，如：北京市朝阳区xxx小区x号楼x单元xxx室",maxlength:"200","show-word-limit":""},model:{value:e.orderForm.address,callback:function(t){e.$set(e.orderForm,"address",t)},expression:"orderForm.address"}})],1),t("el-form-item",{attrs:{label:"故障图片"}},[t("el-upload",{staticClass:"upload-demo",attrs:{action:"#","on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"file-list":e.fileList,"list-type":"picture",multiple:"",limit:5,"on-exceed":e.handleExceed}},[t("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(" 只能上传jpg/png文件，且不超过2MB，最多5张图片 ")])],1)],1),t("el-form-item",{attrs:{label:"备注信息"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"其他需要说明的信息（选填）",maxlength:"200","show-word-limit":""},model:{value:e.orderForm.remarks,callback:function(t){e.$set(e.orderForm,"remarks",t)},expression:"orderForm.remarks"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary",size:"large",loading:e.submitting},on:{click:e.submitOrder}},[e._v(" 提交工单 ")]),t("el-button",{attrs:{size:"large"},on:{click:e.resetForm}},[e._v("重置")])],1)],1)],1)],1)],1),t("el-dialog",{attrs:{visible:e.previewVisible,title:"图片预览"},on:{"update:visible":function(t){e.previewVisible=t}}},[t("img",{attrs:{width:"100%",src:e.previewImageUrl,alt:"预览图片"}})])],1)},X=[],Z={name:"CustomerCreateOrder",computed:{...(0,p.L8)(["currentUser"])},data(){return{submitting:!1,previewVisible:!1,previewImageUrl:"",fileList:[],orderForm:{appliance_type:"",brand_model:"",urgency:"low",problem_description:"",contact_name:"",contact_phone:"",address:"",remarks:""},orderRules:{appliance_type:[{required:!0,message:"请选择家电类型",trigger:"change"}],problem_description:[{required:!0,message:"请描述故障现象",trigger:"blur"},{min:3,message:"故障描述至少3个字符",trigger:"blur"}],contact_name:[{required:!0,message:"请输入联系人姓名",trigger:"blur"}],contact_phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],address:[{required:!0,message:"请输入详细地址",trigger:"blur"},{min:3,message:"地址信息至少3个字符",trigger:"blur"}]}}},mounted(){this.currentUser&&(this.orderForm.contact_name=this.currentUser.name||"",this.orderForm.contact_phone=this.currentUser.phone||"")},methods:{async submitOrder(){try{await this.$refs.orderForm.validate(),this.submitting=!0;const e={...this.orderForm,purchase_date:this.orderForm.purchase_date||null,images:this.fileList.map(e=>e.url||e.response?.url).filter(Boolean)};console.log("[前端] 提交工单数据:",e);const t=await this.$store.dispatch("createOrder",e);t.success?(this.$message.success("工单提交成功！"),this.$safeRouter.push("/customer/orders")):this.$message.error(t.message||"提交失败，请重试")}catch(e){console.error("[前端] 提交工单失败:",e),"validation failed"!==e.message&&this.$message.error("提交失败，请检查网络连接")}finally{this.submitting=!1}},resetForm(){this.$refs.orderForm.resetFields(),this.fileList=[]},handlePreview(e){this.previewImageUrl=e.url,this.previewVisible=!0},handleRemove(e,t){this.fileList=t},beforeUpload(e){const t="image/jpeg"===e.type||"image/png"===e.type,s=e.size/1024/1024<2;if(!t)return this.$message.error("只能上传 JPG/PNG 格式的图片!"),!1;if(!s)return this.$message.error("图片大小不能超过 2MB!"),!1;const a=new FileReader;return a.onload=t=>{this.fileList.push({name:e.name,url:t.target.result})},a.readAsDataURL(e),!1},handleExceed(e,t){this.$message.warning(`最多只能上传5张图片，当前选择了 ${e.length} 张图片，共选择了 ${e.length+t.length} 张图片`)}}},ee=Z,te=(0,n.A)(ee,Y,X,!1,null,"06ae7e52",null),se=te.exports,ae=function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-container worker-theme"},[t("div",{staticClass:"login-form"},[t("div",{staticClass:"login-header"},[t("div",{staticClass:"back-btn"},[t("el-button",{attrs:{icon:"el-icon-arrow-left"},on:{click:function(t){return e.$router.push("/")}}},[e._v("返回首页")])],1),t("h2",[e._v("工人登录")]),t("p",[e._v("永盛制冷维修有限公司-家电售卖与维修")])]),t("el-form",{ref:"loginForm",attrs:{model:e.loginForm,rules:e.loginRules,"label-width":"0"},nativeOn:{submit:function(t){return t.preventDefault(),e.handleLogin.apply(null,arguments)}}},[t("el-form-item",{attrs:{prop:"username"}},[t("el-input",{attrs:{placeholder:"请输入用户名","prefix-icon":"el-icon-user",size:"large"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}})],1),t("el-form-item",{attrs:{prop:"password"}},[t("el-input",{attrs:{type:"password",placeholder:"请输入密码","prefix-icon":"el-icon-lock",size:"large","show-password":""},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}})],1),t("el-form-item",[t("el-button",{staticStyle:{width:"100%"},attrs:{type:"success",size:"large",loading:e.$store.state.loading},on:{click:e.handleLogin}},[e._v(" 登录 ")])],1)],1)],1)])},re=[],ie={name:"WorkerLogin",data(){return{loginForm:{username:"",password:""},loginRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]}}},methods:{async handleLogin(){try{await this.$refs.loginForm.validate();const e=await this.$store.dispatch("login",{credentials:this.loginForm,userType:"worker"});e.success?(this.$message.success("登录成功"),this.$router.push("/worker/home")):this.$message.error(e.message)}catch(e){console.error("登录失败:",e)}}}},le=ie,oe=(0,n.A)(le,ae,re,!1,null,"bd90b726",null),ne=oe.exports,ce=function(){var e=this,t=e._self._c;return t("div",{staticClass:"worker-home"},[t("el-container",[t("el-header",{staticClass:"header"},[t("div",{staticClass:"header-left"},[t("h2",[e._v("工人端 - 永盛制冷维修有限公司")])]),t("div",{staticClass:"header-right"},[t("el-dropdown",{on:{command:e.handleCommand}},[t("span",{staticClass:"user-info"},[t("i",{staticClass:"el-icon-user"}),e._v(" "+e._s(e.currentUser.name)+" "),t("i",{staticClass:"el-icon-arrow-down"})]),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{attrs:{command:"profile"}},[e._v("个人信息")]),t("el-dropdown-item",{attrs:{command:"logout",divided:""}},[e._v("退出登录")])],1)],1)],1)]),t("el-main",{staticClass:"main-content"},[t("div",{staticClass:"welcome-section"},[t("h1",[e._v("欢迎回来，"+e._s(e.currentUser.name)+"师傅！")]),t("p",[e._v("今天也要为客户提供优质的维修服务哦～")])]),t("el-row",{staticClass:"stats-row",attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-icon pending"},[t("i",{staticClass:"el-icon-time"})]),t("div",{staticClass:"stat-info"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.workerStats.availableOrders))]),t("div",{staticClass:"stat-label"},[e._v("可接工单")])])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-icon processing"},[t("i",{staticClass:"el-icon-s-tools"})]),t("div",{staticClass:"stat-info"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.workerStats.inProgress))]),t("div",{staticClass:"stat-label"},[e._v("进行中")])])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-icon completed"},[t("i",{staticClass:"el-icon-circle-check"})]),t("div",{staticClass:"stat-info"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.workerStats.completed))]),t("div",{staticClass:"stat-label"},[e._v("已完成")])])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-icon rating"},[t("i",{staticClass:"el-icon-star-on"})]),t("div",{staticClass:"stat-info"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.workerStats.avgRating))]),t("div",{staticClass:"stat-label"},[e._v("平均评分")])])])])],1)],1),t("div",{staticClass:"quick-actions"},[t("h2",[e._v("快捷操作")]),t("el-row",{staticClass:"action-row",attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"action-card",attrs:{shadow:"hover"},nativeOn:{click:function(t){return e.$safeRouter.push("/worker/available-orders")}}},[t("div",{staticClass:"card-content"},[t("div",{staticClass:"action-icon"},[t("i",{staticClass:"el-icon-document-add"})]),t("h3",[e._v("接单")]),t("p",[e._v("查看并接受新的维修工单")])])])],1),t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"action-card",attrs:{shadow:"hover"},nativeOn:{click:function(t){return e.$safeRouter.push("/worker/orders")}}},[t("div",{staticClass:"card-content"},[t("div",{staticClass:"action-icon"},[t("i",{staticClass:"el-icon-s-order"})]),t("h3",[e._v("我的工单")]),t("p",[e._v("管理正在进行的维修工单")])])])],1),t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"action-card",attrs:{shadow:"hover"},nativeOn:{click:function(t){return e.$safeRouter.push("/worker/salary")}}},[t("div",{staticClass:"card-content"},[t("div",{staticClass:"action-icon"},[t("i",{staticClass:"el-icon-money"})]),t("h3",[e._v("我的薪资")]),t("p",[e._v("查看薪资明细和历史记录")])])])],1)],1)],1),e.todayOrders.length>0?t("div",{staticClass:"today-orders"},[t("h2",[e._v("今日工单")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.todayOrders}},[t("el-table-column",{attrs:{prop:"id",label:"工单号",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("span",{staticClass:"order-id"},[e._v("#"+e._s(s.row.id))])]}}],null,!1,4017610379)}),t("el-table-column",{attrs:{prop:"appliance_type",label:"家电类型",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getApplianceTypeName(t.row.appliance_type))+" ")]}}],null,!1,1194158546)}),t("el-table-column",{attrs:{prop:"customer_name",label:"客户",width:"100"}}),t("el-table-column",{attrs:{prop:"address",label:"地址","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"preferred_time",label:"预约时间",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getTimeSlotText(t.row.preferred_time))+" ")]}}],null,!1,1909616457)}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusType(s.row.status),size:"small"}},[e._v(" "+e._s(e.getStatusText(s.row.status))+" ")])]}}],null,!1,1417625921)}),t("el-table-column",{attrs:{label:"操作",width:"150"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.viewOrderDetail(s.row)}}},[e._v("查看")]),"accepted"===s.row.status?t("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.startRepair(s.row)}}},[e._v(" 开始 ")]):e._e()]}}],null,!1,3943570475)})],1)],1):t("div",{staticClass:"empty-state"},[t("i",{staticClass:"el-icon-document"}),t("p",[e._v("今日暂无工单")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.$safeRouter.push("/worker/available-orders")}}},[e._v(" 去接单 ")])],1)],1)],1)],1)},de=[],ue={name:"WorkerHome",computed:{...(0,p.L8)(["currentUser"]),workerOrders(){return this.$store.state.orders||[]},todayOrders(){const e=(new Date).toDateString();return this.workerOrders.filter(t=>{const s=new Date(t.preferred_date).toDateString();return s===e&&["accepted","in_progress"].includes(t.status)})},workerStats(){const e={availableOrders:0,inProgress:0,completed:0,avgRating:0};e.availableOrders=this.$store.state.availableOrders.length||0,e.inProgress=this.workerOrders.filter(e=>["accepted","in_progress"].includes(e.status)).length,e.completed=this.workerOrders.filter(e=>"completed"===e.status).length;const t=this.workerOrders.filter(e=>"completed"===e.status&&e.rating);if(t.length>0){const s=t.reduce((e,t)=>e+t.rating,0);e.avgRating=(s/t.length).toFixed(1)}else e.avgRating="暂无评分";return e}},async created(){await this.$store.dispatch("fetchWorkerOrders"),await this.$store.dispatch("fetchAvailableOrders")},methods:{async handleCommand(e){if("logout"===e)try{await this.$confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await this.$store.dispatch("logout");e.success&&(this.$message.success("退出登录成功"),this.$safeRouter.push("/worker/login"))}catch(t){console.log("用户取消退出登录")}else"profile"===e&&this.$message.info("个人信息功能开发中")},viewOrderDetail(e){this.$safeRouter.push(`/worker/orders?id=${e.id}`)},startRepair(e){this.$safeRouter.push(`/worker/orders?id=${e.id}&action=start`)},getApplianceTypeName(e){const t={washing_machine:"洗衣机",refrigerator:"冰箱",air_conditioner:"空调",television:"电视",microwave:"微波炉",water_heater:"热水器",range_hood:"油烟机",gas_stove:"燃气灶",other:"其他"};return t[e]||e},getStatusType(e){const t={pending:"warning",accepted:"info",in_progress:"primary",completed:"success",cancelled:"danger"};return t[e]||"info"},getStatusText(e){const t={pending:"待接单",accepted:"已接单",in_progress:"维修中",completed:"已完成",cancelled:"已取消"};return t[e]||"未知状态"},getTimeSlotText(e){const t={morning:"上午",afternoon:"下午",evening:"晚上"};return t[e]||""}}},pe=ue,me=(0,n.A)(pe,ce,de,!1,null,"14ad8eb3",null),he=me.exports,ge=function(){var e=this,t=e._self._c;return t("div",{staticClass:"worker-orders"},[t("el-container",[t("el-header",{staticClass:"header"},[t("div",{staticClass:"header-left"},[t("el-button",{attrs:{icon:"el-icon-arrow-left"},on:{click:function(t){return e.$safeRouter.push("/worker/home")}}},[e._v("返回首页")]),t("h2",[e._v("我的工单")])],1),t("div",{staticClass:"header-right"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.$safeRouter.push("/worker/available-orders")}}},[e._v(" 接单 ")])],1)]),t("el-main",{staticClass:"main-content"},[t("el-card",{staticClass:"filter-card",attrs:{shadow:"never"}},[t("el-row",{attrs:{gutter:20,type:"flex",align:"middle"}},[t("el-col",{attrs:{span:4}},[t("el-select",{attrs:{placeholder:"工单状态",clearable:""},on:{change:e.handleFilter},model:{value:e.filterStatus,callback:function(t){e.filterStatus=t},expression:"filterStatus"}},[t("el-option",{attrs:{label:"全部状态",value:""}}),t("el-option",{attrs:{label:"已接单",value:"accepted"}}),t("el-option",{attrs:{label:"维修中",value:"in_progress"}}),t("el-option",{attrs:{label:"已完成",value:"completed"}})],1)],1),t("el-col",{attrs:{span:4}},[t("el-select",{attrs:{placeholder:"家电类型",clearable:""},on:{change:e.handleFilter},model:{value:e.filterType,callback:function(t){e.filterType=t},expression:"filterType"}},[t("el-option",{attrs:{label:"全部类型",value:""}}),t("el-option",{attrs:{label:"洗衣机",value:"washing_machine"}}),t("el-option",{attrs:{label:"冰箱",value:"refrigerator"}}),t("el-option",{attrs:{label:"空调",value:"air_conditioner"}}),t("el-option",{attrs:{label:"电视",value:"television"}}),t("el-option",{attrs:{label:"微波炉",value:"microwave"}}),t("el-option",{attrs:{label:"热水器",value:"water_heater"}}),t("el-option",{attrs:{label:"油烟机",value:"range_hood"}}),t("el-option",{attrs:{label:"燃气灶",value:"gas_stove"}}),t("el-option",{attrs:{label:"其他",value:"other"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-input",{attrs:{placeholder:"搜索工单号、客户姓名或地址","prefix-icon":"el-icon-search",clearable:""},on:{input:e.handleSearch},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}})],1),t("el-col",{attrs:{span:4}},[t("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.refreshOrders}},[e._v("刷新")])],1),t("el-col",{attrs:{span:4}},[t("el-button",{attrs:{icon:"el-icon-data-line"},on:{click:e.showHistory}},[e._v("维修历史")])],1)],1)],1),t("el-row",{staticClass:"stats-row",attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"stat-card"},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number accepted"},[e._v(e._s(e.orderStats.accepted))]),t("div",{staticClass:"stat-label"},[e._v("已接单")])])])],1),t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"stat-card"},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number processing"},[e._v(e._s(e.orderStats.processing))]),t("div",{staticClass:"stat-label"},[e._v("维修中")])])])],1),t("el-col",{attrs:{span:8}},[t("el-card",{staticClass:"stat-card"},[t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number completed"},[e._v(e._s(e.orderStats.completed))]),t("div",{staticClass:"stat-label"},[e._v("已完成")])])])],1)],1),t("el-card",{staticClass:"orders-card"},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("工单列表")]),t("small",[e._v("共 "+e._s(e.filteredOrders.length)+" 条记录")])]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.paginatedOrders,"row-class-name":"order-row"},on:{"row-click":e.viewOrderDetail}},[t("el-table-column",{attrs:{prop:"id",label:"工单号",width:"100",fixed:"left"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("span",{staticClass:"order-id"},[e._v("#"+e._s(s.row.id))])]}}])}),t("el-table-column",{attrs:{prop:"appliance_type",label:"家电类型",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getApplianceTypeName(t.row.appliance_type))+" ")]}}])}),t("el-table-column",{attrs:{prop:"brand_model",label:"品牌型号",width:"150","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"customer_name",label:"客户",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.contact_name)+" ")]}}])}),t("el-table-column",{attrs:{prop:"address",label:"地址","min-width":"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusType(s.row.status),size:"small"}},[e._v(" "+e._s(e.getStatusText(s.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"urgency",label:"紧急程度",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getUrgencyType(s.row.urgency),size:"small"}},[e._v(" "+e._s(e.getUrgencyText(s.row.urgency))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"preferred_date",label:"预约时间",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDate(t.row.preferred_date))+" "+e._s(e.getTimeSlotText(t.row.preferred_time))+" ")]}}])}),t("el-table-column",{attrs:{label:"操作",width:"200",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{size:"mini"},on:{click:function(t){return t.stopPropagation(),e.viewOrderDetail(s.row)}}},[e._v("查看")]),"accepted"===s.row.status?t("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return t.stopPropagation(),e.startRepair(s.row)}}},[e._v(" 开始维修 ")]):e._e(),"in_progress"===s.row.status?t("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(t){return t.stopPropagation(),e.updateProgress(s.row)}}},[e._v(" 更新进度 ")]):e._e(),"in_progress"===s.row.status?t("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(t){return t.stopPropagation(),e.completeRepair(s.row)}}},[e._v(" 完成维修 ")]):e._e()]}}])})],1),t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.filteredOrders.length},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)],1),t("el-dialog",{attrs:{visible:e.detailVisible,title:"工单详情",width:"900px","before-close":e.handleDetailClose},on:{"update:visible":function(t){e.detailVisible=t}}},[e.selectedOrder?t("worker-order-detail",{attrs:{order:e.selectedOrder},on:{refresh:e.refreshOrders,"start-repair":e.startRepair,"update-progress":e.updateProgress,"complete-repair":e.completeRepair}}):e._e()],1),t("el-dialog",{attrs:{visible:e.progressVisible,title:"更新维修进度",width:"600px"},on:{"update:visible":function(t){e.progressVisible=t}}},[e.selectedOrder?t("progress-update",{attrs:{order:e.selectedOrder},on:{submit:e.handleProgressSubmit,cancel:function(t){e.progressVisible=!1}}}):e._e()],1),t("el-dialog",{attrs:{visible:e.completeVisible,title:"完成维修",width:"600px"},on:{"update:visible":function(t){e.completeVisible=t}}},[e.selectedOrder?t("complete-repair",{attrs:{order:e.selectedOrder},on:{submit:e.handleCompleteSubmit,cancel:function(t){e.completeVisible=!1}}}):e._e()],1)],1)},ve=[],_e=function(){var e=this,t=e._self._c;return t("div",{staticClass:"worker-order-detail"},[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"工单号"}},[t("span",{staticClass:"order-id"},[e._v("#"+e._s(e.order.id))])]),t("el-descriptions-item",{attrs:{label:"状态"}},[t("el-tag",{attrs:{type:e.getStatusType(e.order.status)}},[e._v(" "+e._s(e.getStatusText(e.order.status))+" ")])],1),t("el-descriptions-item",{attrs:{label:"家电类型"}},[e._v(" "+e._s(e.getApplianceTypeName(e.order.appliance_type))+" ")]),t("el-descriptions-item",{attrs:{label:"品牌型号"}},[e._v(" "+e._s(e.order.brand_model)+" ")]),t("el-descriptions-item",{attrs:{label:"紧急程度"}},[t("el-tag",{attrs:{type:e.getUrgencyType(e.order.urgency),size:"small"}},[e._v(" "+e._s(e.getUrgencyText(e.order.urgency))+" ")])],1),t("el-descriptions-item",{attrs:{label:"购买时间"}},[e._v(" "+e._s(e.formatDate(e.order.purchase_date))+" ")]),t("el-descriptions-item",{attrs:{label:"客户姓名"}},[e._v(" "+e._s(e.order.contact_name)+" ")]),t("el-descriptions-item",{attrs:{label:"联系电话"}},[t("a",{staticClass:"phone-link",attrs:{href:`tel:${e.order.contact_phone}`}},[t("i",{staticClass:"el-icon-phone"}),e._v(" "+e._s(e.order.contact_phone)+" ")])]),t("el-descriptions-item",{attrs:{label:"创建时间"}},[e._v(" "+e._s(e.formatDate(e.order.created_at))+" ")]),t("el-descriptions-item",{attrs:{label:"预约时间"}},[e._v(" "+e._s(e.formatDate(e.order.preferred_date))+" "+e._s(e.getTimeSlotText(e.order.preferred_time))+" ")])],1),t("el-divider",{attrs:{"content-position":"left"}},[e._v("详细地址")]),t("div",{staticClass:"address-section"},[t("p",{staticClass:"address"},[e._v(e._s(e.order.address))]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.openMap}},[t("i",{staticClass:"el-icon-location"}),e._v(" 导航 ")])],1),t("el-divider",{attrs:{"content-position":"left"}},[e._v("故障描述")]),t("p",{staticClass:"description"},[e._v(e._s(e.order.problem_description))]),e.order.remarks?t("el-divider",{attrs:{"content-position":"left"}},[e._v("备注信息")]):e._e(),e.order.remarks?t("p",{staticClass:"remarks"},[e._v(e._s(e.order.remarks))]):e._e(),e.order.images&&e.order.images.length>0?t("el-divider",{attrs:{"content-position":"left"}},[e._v("故障图片")]):e._e(),e.order.images&&e.order.images.length>0?t("div",{staticClass:"images"},e._l(e.order.images,function(s,a){return t("el-image",{key:a,staticClass:"image-item",attrs:{src:s,"preview-src-list":e.order.images,fit:"cover"}})}),1):e._e(),e.order.progress&&e.order.progress.length>0?t("el-divider",{attrs:{"content-position":"left"}},[e._v("维修进度")]):e._e(),e.order.progress&&e.order.progress.length>0?t("el-timeline",e._l(e.order.progress,function(s,a){return t("el-timeline-item",{key:a,attrs:{timestamp:e.formatDate(s.created_at),placement:"top"}},[t("el-card",[t("h4",[e._v(e._s(s.title))]),t("p",[e._v(e._s(s.description))]),s.images&&s.images.length>0?t("div",{staticClass:"progress-images"},e._l(s.images,function(e,a){return t("el-image",{key:a,staticClass:"progress-image",attrs:{src:e,"preview-src-list":s.images,fit:"cover"}})}),1):e._e()])],1)}),1):e._e(),t("div",{staticClass:"actions"},["pending"===e.order.status&&e.showAcceptButton?t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.$emit("accept",e.order)}}},[e._v(" 接单 ")]):e._e(),"accepted"===e.order.status?t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.$emit("start-repair",e.order)}}},[e._v(" 开始维修 ")]):e._e(),"in_progress"===e.order.status?t("el-button",{attrs:{type:"info"},on:{click:function(t){return e.$emit("update-progress",e.order)}}},[e._v(" 更新进度 ")]):e._e(),"in_progress"===e.order.status?t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.$emit("complete-repair",e.order)}}},[e._v(" 完成维修 ")]):e._e(),t("el-button",{on:{click:e.callCustomer}},[t("i",{staticClass:"el-icon-phone"}),e._v(" 联系客户 ")])],1)],1)},fe=[],be={name:"WorkerOrderDetail",props:{order:{type:Object,required:!0},showAcceptButton:{type:Boolean,default:!1}},methods:{callCustomer(){window.location.href=`tel:${this.order.contact_phone}`},openMap(){const e=encodeURIComponent(this.order.address);window.open(`https://uri.amap.com/navigation?to=${e}`)},getApplianceTypeName(e){const t={washing_machine:"洗衣机",refrigerator:"冰箱",air_conditioner:"空调",television:"电视",microwave:"微波炉",water_heater:"热水器",range_hood:"油烟机",gas_stove:"燃气灶",other:"其他"};return t[e]||e},getStatusType(e){const t={pending:"warning",accepted:"info",in_progress:"primary",completed:"success",cancelled:"danger"};return t[e]||"info"},getStatusText(e){const t={pending:"待接单",accepted:"已接单",in_progress:"维修中",completed:"已完成",cancelled:"已取消"};return t[e]||"未知状态"},getUrgencyType(e){const t={low:"info",medium:"warning",high:"danger"};return t[e]||"info"},getUrgencyText(e){const t={low:"一般",medium:"紧急",high:"非常紧急"};return t[e]||"一般"},getTimeSlotText(e){const t={morning:"上午 (9:00-12:00)",afternoon:"下午 (13:00-17:00)",evening:"晚上 (18:00-20:00)"};return t[e]||""},formatDate(e){if(!e)return"-";const t=new Date(e);return t.toLocaleString("zh-CN")}}},ye=be,we=(0,n.A)(ye,_e,fe,!1,null,"66f8ef34",null),Ce=we.exports,Se=function(){var e=this,t=e._self._c;return t("div",{staticClass:"progress-update"},[t("el-form",{ref:"progressForm",attrs:{model:e.progressForm,rules:e.progressRules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"进度标题",prop:"title"}},[t("el-input",{attrs:{placeholder:"请输入进度标题，如：开始检查、发现问题、更换配件等",maxlength:"50","show-word-limit":""},model:{value:e.progressForm.title,callback:function(t){e.$set(e.progressForm,"title",t)},expression:"progressForm.title"}})],1),t("el-form-item",{attrs:{label:"详细描述",prop:"description"}},[t("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请详细描述当前的维修进度和发现的问题...",maxlength:"500","show-word-limit":""},model:{value:e.progressForm.description,callback:function(t){e.$set(e.progressForm,"description",t)},expression:"progressForm.description"}})],1),t("el-form-item",{attrs:{label:"进度图片"}},[t("el-upload",{staticClass:"upload-demo",attrs:{action:"#","on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"file-list":e.fileList,"list-type":"picture",multiple:"",limit:5,"on-exceed":e.handleExceed}},[t("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(" 可上传维修过程图片，只能上传jpg/png文件，且不超过2MB，最多5张 ")])],1)],1),t("el-form-item",{attrs:{label:"预计完成"}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择预计完成时间","picker-options":e.datePickerOptions},model:{value:e.progressForm.estimated_completion,callback:function(t){e.$set(e.progressForm,"estimated_completion",t)},expression:"progressForm.estimated_completion"}})],1),e.showPartsSection?t("el-form-item",{attrs:{label:"需要配件"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"如需要配件，请详细说明配件名称、型号、数量等",maxlength:"200","show-word-limit":""},model:{value:e.progressForm.required_parts,callback:function(t){e.$set(e.progressForm,"required_parts",t)},expression:"progressForm.required_parts"}})],1):e._e(),t("el-form-item",[t("el-checkbox",{model:{value:e.showPartsSection,callback:function(t){e.showPartsSection=t},expression:"showPartsSection"}},[e._v("需要配件")])],1)],1),t("div",{staticClass:"actions"},[t("el-button",{on:{click:function(t){return e.$emit("cancel")}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.submitting},on:{click:e.submitProgress}},[e._v(" 提交进度 ")])],1),t("el-dialog",{attrs:{visible:e.previewVisible,title:"图片预览"},on:{"update:visible":function(t){e.previewVisible=t}}},[t("img",{attrs:{width:"100%",src:e.previewImageUrl,alt:"预览图片"}})])],1)},ke=[],Te={name:"ProgressUpdate",props:{order:{type:Object,required:!0}},data(){return{submitting:!1,previewVisible:!1,previewImageUrl:"",fileList:[],showPartsSection:!1,progressForm:{title:"",description:"",estimated_completion:"",required_parts:""},progressRules:{title:[{required:!0,message:"请输入进度标题",trigger:"blur"},{min:2,message:"标题至少2个字符",trigger:"blur"}],description:[{required:!0,message:"请输入详细描述",trigger:"blur"},{min:10,message:"描述至少10个字符",trigger:"blur"}]},datePickerOptions:{disabledDate(e){return e.getTime()<Date.now()-864e5}}}},methods:{async submitProgress(){try{await this.$refs.progressForm.validate(),this.submitting=!0;const e={orderId:this.order.id,...this.progressForm,images:this.fileList.map(e=>e.url||e.response?.url).filter(Boolean)};console.log("[前端] 提交维修进度:",e);const t=await this.$store.dispatch("updateProgress",{orderId:this.order.id,progressData:e});t.success?(this.$message.success("维修进度已更新"),this.$emit("submit",e)):this.$message.error(t.message||"更新进度失败")}catch(e){console.error("[前端] 提交进度失败:",e),"validation failed"!==e.message&&this.$message.error("提交失败，请检查网络连接")}finally{this.submitting=!1}},handlePreview(e){this.previewImageUrl=e.url,this.previewVisible=!0},handleRemove(e,t){this.fileList=t},beforeUpload(e){const t="image/jpeg"===e.type||"image/png"===e.type,s=e.size/1024/1024<2;if(!t)return this.$message.error("只能上传 JPG/PNG 格式的图片!"),!1;if(!s)return this.$message.error("图片大小不能超过 2MB!"),!1;const a=new FileReader;return a.onload=t=>{this.fileList.push({name:e.name,url:t.target.result})},a.readAsDataURL(e),!1},handleExceed(e,t){this.$message.warning(`最多只能上传5张图片，当前选择了 ${e.length} 张图片，共选择了 ${e.length+t.length} 张图片`)}}},$e=Te,Oe=(0,n.A)($e,Se,ke,!1,null,"0187d943",null),xe=Oe.exports,Fe=function(){var e=this,t=e._self._c;return t("div",{staticClass:"complete-repair"},[t("el-form",{ref:"completeForm",attrs:{model:e.completeForm,rules:e.completeRules,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"维修结果",prop:"result"}},[t("el-radio-group",{model:{value:e.completeForm.result,callback:function(t){e.$set(e.completeForm,"result",t)},expression:"completeForm.result"}},[t("el-radio",{attrs:{label:"success"}},[e._v("维修成功")]),t("el-radio",{attrs:{label:"partial"}},[e._v("部分修复")]),t("el-radio",{attrs:{label:"failed"}},[e._v("无法修复")])],1)],1),t("el-form-item",{attrs:{label:"问题原因",prop:"cause"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请详细说明故障的原因...",maxlength:"300","show-word-limit":""},model:{value:e.completeForm.cause,callback:function(t){e.$set(e.completeForm,"cause",t)},expression:"completeForm.cause"}})],1),t("el-form-item",{attrs:{label:"解决方案",prop:"solution"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请详细说明采取的解决方案...",maxlength:"300","show-word-limit":""},model:{value:e.completeForm.solution,callback:function(t){e.$set(e.completeForm,"solution",t)},expression:"completeForm.solution"}})],1),e.showPartsSection?t("el-form-item",{attrs:{label:"更换配件"}},[t("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请列出更换的配件名称、型号、数量等",maxlength:"200","show-word-limit":""},model:{value:e.completeForm.replaced_parts,callback:function(t){e.$set(e.completeForm,"replaced_parts",t)},expression:"completeForm.replaced_parts"}})],1):e._e(),t("el-form-item",[t("el-checkbox",{model:{value:e.showPartsSection,callback:function(t){e.showPartsSection=t},expression:"showPartsSection"}},[e._v("更换了配件")])],1),t("el-form-item",{attrs:{label:"维修费用",prop:"cost"}},[t("el-input-number",{staticStyle:{width:"200px"},attrs:{min:0,max:9999,precision:2,placeholder:"请输入维修费用"},model:{value:e.completeForm.cost,callback:function(t){e.$set(e.completeForm,"cost",t)},expression:"completeForm.cost"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("元")])],1),t("el-form-item",{attrs:{label:"完成图片"}},[t("el-upload",{staticClass:"upload-demo",attrs:{action:"#","on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"file-list":e.fileList,"list-type":"picture",multiple:"",limit:5,"on-exceed":e.handleExceed}},[t("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(" 可上传维修完成后的图片，只能上传jpg/png文件，且不超过2MB，最多5张 ")])],1)],1),t("el-form-item",{attrs:{label:"使用建议"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"给客户的使用建议和保养提醒（选填）",maxlength:"300","show-word-limit":""},model:{value:e.completeForm.suggestions,callback:function(t){e.$set(e.completeForm,"suggestions",t)},expression:"completeForm.suggestions"}})],1),"success"===e.completeForm.result?t("el-form-item",{attrs:{label:"保修期限"}},[t("el-select",{attrs:{placeholder:"请选择保修期限"},model:{value:e.completeForm.warranty_period,callback:function(t){e.$set(e.completeForm,"warranty_period",t)},expression:"completeForm.warranty_period"}},[t("el-option",{attrs:{label:"1个月",value:"1"}}),t("el-option",{attrs:{label:"3个月",value:"3"}}),t("el-option",{attrs:{label:"6个月",value:"6"}}),t("el-option",{attrs:{label:"1年",value:"12"}})],1)],1):e._e()],1),t("div",{staticClass:"actions"},[t("el-button",{on:{click:function(t){return e.$emit("cancel")}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.submitting},on:{click:e.submitComplete}},[e._v(" 完成维修 ")])],1),t("el-dialog",{attrs:{visible:e.previewVisible,title:"图片预览"},on:{"update:visible":function(t){e.previewVisible=t}}},[t("img",{attrs:{width:"100%",src:e.previewImageUrl,alt:"预览图片"}})])],1)},Ae=[],De={name:"CompleteRepair",props:{order:{type:Object,required:!0}},data(){return{submitting:!1,previewVisible:!1,previewImageUrl:"",fileList:[],showPartsSection:!1,completeForm:{result:"success",cause:"",solution:"",replaced_parts:"",cost:0,suggestions:"",warranty_period:"3"},completeRules:{result:[{required:!0,message:"请选择维修结果",trigger:"change"}],cause:[{required:!0,message:"请说明问题原因",trigger:"blur"},{min:3,message:"原因说明至少3个字符",trigger:"blur"}],solution:[{required:!0,message:"请说明解决方案",trigger:"blur"},{min:3,message:"解决方案至少3个字符",trigger:"blur"}],cost:[{required:!0,message:"请输入维修费用",trigger:"blur"},{type:"number",min:0,message:"费用不能为负数",trigger:"blur"}],summary:[{required:!0,message:"请填写维修总结",trigger:"blur"},{min:20,message:"维修总结至少20个字符",trigger:"blur"}]}}},methods:{async submitComplete(){try{await this.$refs.completeForm.validate(),this.submitting=!0;const e={orderId:this.order.id,...this.completeForm,images:this.fileList.map(e=>e.url||e.response?.url).filter(Boolean),completed_at:(new Date).toISOString()};console.log("[前端] 完成维修:",e);const t=await this.$store.dispatch("completeRepair",{orderId:this.order.id,repairData:e});t.success?(this.$message.success("维修已完成"),this.$emit("submit",e)):this.$message.error(t.message||"完成维修失败")}catch(e){console.error("[前端] 完成维修失败:",e),"validation failed"!==e.message&&this.$message.error("提交失败，请检查网络连接")}finally{this.submitting=!1}},handlePreview(e){this.previewImageUrl=e.url,this.previewVisible=!0},handleRemove(e,t){this.fileList=t},beforeUpload(e){const t="image/jpeg"===e.type||"image/png"===e.type,s=e.size/1024/1024<2;if(!t)return this.$message.error("只能上传 JPG/PNG 格式的图片!"),!1;if(!s)return this.$message.error("图片大小不能超过 2MB!"),!1;const a=new FileReader;return a.onload=t=>{this.fileList.push({name:e.name,url:t.target.result})},a.readAsDataURL(e),!1},handleExceed(e,t){this.$message.warning(`最多只能上传5张图片，当前选择了 ${e.length} 张图片，共选择了 ${e.length+t.length} 张图片`)}}},Ue=De,Re=(0,n.A)(Ue,Fe,Ae,!1,null,"9b9ec194",null),ze=Re.exports,Pe={name:"WorkerOrders",components:{WorkerOrderDetail:Ce,ProgressUpdate:xe,CompleteRepair:ze},computed:{...(0,p.L8)(["currentUser"]),orders(){return this.$store.state.orders||[]},filteredOrders(){let e=this.orders.filter(e=>["accepted","in_progress","completed"].includes(e.status));if(this.filterStatus&&(e=e.filter(e=>e.status===this.filterStatus)),this.filterType&&(e=e.filter(e=>e.appliance_type===this.filterType)),this.searchKeyword){const t=this.searchKeyword.toLowerCase();e=e.filter(e=>e.id.toString().includes(t)||e.contact_name.toLowerCase().includes(t)||e.address.toLowerCase().includes(t))}return e},paginatedOrders(){const e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;return this.filteredOrders.slice(e,t)},orderStats(){const e={accepted:0,processing:0,completed:0};return this.orders.forEach(t=>{switch(t.status){case"accepted":e.accepted++;break;case"in_progress":e.processing++;break;case"completed":e.completed++;break}}),e}},data(){return{loading:!1,detailVisible:!1,progressVisible:!1,completeVisible:!1,selectedOrder:null,filterStatus:"",filterType:"",searchKeyword:"",currentPage:1,pageSize:10}},async created(){await this.refreshOrders();const{id:e,action:t}=this.$route.query;if(e){const s=this.orders.find(t=>t.id.toString()===e);s&&(this.selectedOrder=s,"start"===t?this.startRepair(s):this.detailVisible=!0)}},methods:{async refreshOrders(){this.loading=!0;try{await this.$store.dispatch("fetchWorkerOrders")}catch(e){console.error("[前端] 获取工单列表失败:",e),this.$message.error("获取工单列表失败")}finally{this.loading=!1}},handleFilter(){this.currentPage=1},handleSearch(){this.currentPage=1},handleSizeChange(e){this.pageSize=e,this.currentPage=1},handleCurrentChange(e){this.currentPage=e},showHistory(){this.$message.info("维修历史功能开发中")},viewOrderDetail(e){this.selectedOrder=e,this.detailVisible=!0},handleDetailClose(){this.detailVisible=!1,this.selectedOrder=null,(this.$route.query.id||this.$route.query.action)&&this.$safeRouter.replace({path:this.$route.path,query:{}})},async startRepair(e){try{await this.$confirm(`确定开始维修工单 #${e.id} 吗？`,"开始维修",{confirmButtonText:"开始维修",cancelButtonText:"取消",type:"info"});const t=await this.$store.dispatch("startRepair",e.id);t.success?(this.$message.success("已开始维修"),this.detailVisible=!1,await this.refreshOrders()):this.$message.error(t.message||"开始维修失败")}catch(t){"cancel"!==t&&this.$message.error("开始维修失败")}},updateProgress(e){this.selectedOrder=e,this.progressVisible=!0},async handleProgressSubmit(e){try{console.log("[前端] 更新维修进度:",e),this.progressVisible=!1,await this.refreshOrders()}catch(t){this.$message.error("更新进度失败")}},completeRepair(e){this.selectedOrder=e,this.completeVisible=!0},async handleCompleteSubmit(e){try{console.log("[前端] 完成维修:",e),this.completeVisible=!1,await this.refreshOrders()}catch(t){this.$message.error("完成维修失败")}},getApplianceTypeName(e){const t={washing_machine:"洗衣机",refrigerator:"冰箱",air_conditioner:"空调",television:"电视",microwave:"微波炉",water_heater:"热水器",range_hood:"油烟机",gas_stove:"燃气灶",other:"其他"};return t[e]||e},getStatusType(e){const t={accepted:"info",in_progress:"primary",completed:"success"};return t[e]||"info"},getStatusText(e){const t={accepted:"已接单",in_progress:"维修中",completed:"已完成"};return t[e]||"未知状态"},getUrgencyType(e){const t={low:"info",medium:"warning",high:"danger"};return t[e]||"info"},getUrgencyText(e){const t={low:"一般",medium:"紧急",high:"非常紧急"};return t[e]||"一般"},getTimeSlotText(e){const t={morning:"上午",afternoon:"下午",evening:"晚上"};return t[e]||""},formatDate(e){if(!e)return"-";const t=new Date(e);return t.toLocaleDateString("zh-CN")}}},Le=Pe,Me=(0,n.A)(Le,ge,ve,!1,null,"5b7fecf6",null),Ee=Me.exports,Ie=function(){var e=this,t=e._self._c;return t("div",{staticClass:"worker-salary"},[t("div",{staticClass:"page-header"},[t("h2",[e._v("我的薪资")]),t("el-button",{on:{click:function(t){return e.$router.push("/worker/home")}}},[e._v("返回首页")])],1),t("el-card",{staticClass:"current-salary-card",attrs:{header:"本月薪资概览"}},[e.currentSalary?t("div",{staticClass:"salary-overview"},[t("div",{staticClass:"salary-main"},[t("div",{staticClass:"total-amount"},[t("span",{staticClass:"amount"},[e._v("¥"+e._s(e.formatMoney(e.currentSalary.total_salary)))]),t("span",{staticClass:"label"},[e._v("本月总薪资")])]),t("div",{staticClass:"salary-status"},[t("el-tag",{attrs:{type:e.getStatusType(e.currentSalary.status),size:"medium"}},[e._v(" "+e._s(e.getStatusText(e.currentSalary.status))+" ")])],1)]),t("div",{staticClass:"salary-breakdown"},[t("div",{staticClass:"breakdown-item"},[t("span",{staticClass:"item-label"},[e._v("基础工资")]),t("span",{staticClass:"item-value"},[e._v("¥"+e._s(e.formatMoney(e.currentSalary.base_salary)))])]),t("div",{staticClass:"breakdown-item"},[t("span",{staticClass:"item-label"},[e._v("提成收入")]),t("span",{staticClass:"item-value"},[e._v("¥"+e._s(e.formatMoney(e.currentSalary.commission_amount)))])]),t("div",{staticClass:"breakdown-item"},[t("span",{staticClass:"item-label"},[e._v("绩效奖金")]),t("span",{staticClass:"item-value"},[e._v("¥"+e._s(e.formatMoney(e.currentSalary.performance_bonus)))])]),t("div",{staticClass:"breakdown-item"},[t("span",{staticClass:"item-label"},[e._v("全勤奖")]),t("span",{staticClass:"item-value"},[e._v("¥"+e._s(e.formatMoney(e.currentSalary.attendance_bonus)))])]),t("div",{staticClass:"breakdown-item"},[t("span",{staticClass:"item-label"},[e._v("加班费")]),t("span",{staticClass:"item-value"},[e._v("¥"+e._s(e.formatMoney(e.currentSalary.overtime_pay)))])])]),t("div",{staticClass:"performance-stats"},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.currentSalary.completed_orders))]),t("div",{staticClass:"stat-label"},[e._v("完成工单")])]),t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v("¥"+e._s(e.formatMoney(e.currentSalary.total_revenue)))]),t("div",{staticClass:"stat-label"},[e._v("营收金额")])]),t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.currentSalary.attendance_days))]),t("div",{staticClass:"stat-label"},[e._v("出勤天数")])]),t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s((parseFloat(e.currentSalary.avg_rating)||0).toFixed(1)))]),t("div",{staticClass:"stat-label"},[e._v("平均评分")])])])]):t("div",{staticClass:"no-salary"},[t("el-empty",{attrs:{description:"本月暂无薪资数据"}})],1)]),t("el-card",{staticClass:"history-card",attrs:{header:"历史薪资记录"}},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.salaryHistory}},[t("el-table-column",{attrs:{prop:"year",label:"年份",width:"80"}}),t("el-table-column",{attrs:{prop:"month",label:"月份",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.month)+"月 ")]}}])}),t("el-table-column",{attrs:{prop:"completed_orders",label:"完成工单",width:"100"}}),t("el-table-column",{attrs:{prop:"total_revenue",label:"营收金额",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(e.formatMoney(t.row.total_revenue))+" ")]}}])}),t("el-table-column",{attrs:{prop:"base_salary",label:"基础工资",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(e.formatMoney(t.row.base_salary))+" ")]}}])}),t("el-table-column",{attrs:{prop:"commission_amount",label:"提成收入",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(e.formatMoney(t.row.commission_amount))+" ")]}}])}),t("el-table-column",{attrs:{prop:"total_salary",label:"总薪资",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("span",{staticClass:"total-salary"},[e._v("¥"+e._s(e.formatMoney(s.row.total_salary)))])]}}])}),t("el-table-column",{attrs:{prop:"avg_rating",label:"平均评分",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("el-rate",{attrs:{value:parseFloat(e.row.avg_rating)||0,disabled:"","show-score":""}})]}}])}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusType(s.row.status),size:"small"}},[e._v(" "+e._s(e.getStatusText(s.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.viewSalaryDetail(s.row)}}},[e._v(" 查看详情 ")])]}}])})],1)],1),t("el-dialog",{attrs:{title:"薪资详情",visible:e.detailVisible,width:"600px"},on:{"update:visible":function(t){e.detailVisible=t},close:function(t){e.selectedSalary=null}}},[e.selectedSalary?t("div",{staticClass:"salary-detail"},[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"计算月份"}},[e._v(e._s(e.selectedSalary.year)+"年"+e._s(e.selectedSalary.month)+"月")]),t("el-descriptions-item",{attrs:{label:"完成工单"}},[e._v(e._s(e.selectedSalary.completed_orders)+"单")]),t("el-descriptions-item",{attrs:{label:"营收金额"}},[e._v("¥"+e._s(e.formatMoney(e.selectedSalary.total_revenue)))]),t("el-descriptions-item",{attrs:{label:"出勤天数"}},[e._v(e._s(e.selectedSalary.attendance_days)+"天")]),t("el-descriptions-item",{attrs:{label:"平均评分"}},[t("el-rate",{attrs:{value:parseFloat(e.selectedSalary.avg_rating)||0,disabled:"","show-score":""}})],1),t("el-descriptions-item",{attrs:{label:"计算时间"}},[e._v(e._s(e.formatDate(e.selectedSalary.calculated_at)))])],1),t("div",{staticClass:"salary-breakdown-detail"},[t("h4",[e._v("薪资构成")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.salaryDetails}},[t("el-table-column",{attrs:{prop:"item_name",label:"项目",width:"150"}}),t("el-table-column",{attrs:{prop:"amount",label:"金额",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(e.formatMoney(t.row.amount))+" ")]}}],null,!1,3885200678)}),t("el-table-column",{attrs:{prop:"description",label:"说明"}})],1),t("div",{staticClass:"total-row"},[t("strong",[e._v("总计：¥"+e._s(e.formatMoney(e.selectedSalary.total_salary)))])])],1)],1):e._e()])],1)},Ne=[],qe={name:"WorkerSalary",data(){return{loading:!1,currentSalary:null,salaryHistory:[],detailVisible:!1,selectedSalary:null,salaryDetails:[]}},computed:{currentUser(){return this.$store.state.user}},async created(){await this.loadSalaryData()},methods:{async loadSalaryData(){if(this.currentUser)try{this.loading=!0;const e=await m.A.get(`/api/salary/list/${this.currentUser.id}`);if(e.success){this.salaryHistory=e.data;const t=new Date,s=t.getFullYear(),a=t.getMonth()+1;this.currentSalary=this.salaryHistory.find(e=>e.year===s&&e.month===a)}else this.$message.error(e.message||"获取薪资数据失败")}catch(e){console.error("获取薪资数据失败:",e),this.$message.error("获取薪资数据失败")}finally{this.loading=!1}},async viewSalaryDetail(e){try{this.loading=!0;const t=await m.A.get(`/api/salary/detail/${e.id}`);t.success?(this.selectedSalary=t.data.salary,this.salaryDetails=t.data.details,this.detailVisible=!0):this.$message.error(t.message||"获取薪资详情失败")}catch(t){console.error("获取薪资详情失败:",t),this.$message.error("获取薪资详情失败")}finally{this.loading=!1}},getStatusType(e){const t={draft:"info",confirmed:"warning",paid:"success"};return t[e]||"info"},getStatusText(e){const t={draft:"计算中",confirmed:"已确认",paid:"已发放"};return t[e]||e},formatMoney(e){return e?parseFloat(e).toFixed(2):"0.00"},formatDate(e){if(!e)return"未设置";const t=new Date(e);return t.toLocaleString("zh-CN")}}},Ve=qe,Ge=(0,n.A)(Ve,Ie,Ne,!1,null,"1a6a0717",null),Be=Ge.exports,je=function(){var e=this,t=e._self._c;return t("div",{staticClass:"available-orders"},[t("el-container",[t("el-header",{staticClass:"header"},[t("div",{staticClass:"header-left"},[t("el-button",{attrs:{icon:"el-icon-arrow-left"},on:{click:function(t){return e.$safeRouter.push("/worker/home")}}},[e._v("返回首页")]),t("h2",[e._v("可接工单")])],1),t("div",{staticClass:"header-right"},[t("span",{staticClass:"user-info"},[t("i",{staticClass:"el-icon-user"}),e._v(" "+e._s(e.currentUser.name)+" ")])])]),t("el-main",{staticClass:"main-content"},[t("el-card",{staticClass:"filter-card",attrs:{shadow:"never"}},[t("el-row",{attrs:{gutter:20,type:"flex",align:"middle"}},[t("el-col",{attrs:{span:4}},[t("el-select",{attrs:{placeholder:"家电类型",clearable:""},on:{change:e.handleFilter},model:{value:e.filterType,callback:function(t){e.filterType=t},expression:"filterType"}},[t("el-option",{attrs:{label:"全部类型",value:""}}),t("el-option",{attrs:{label:"洗衣机",value:"washing_machine"}}),t("el-option",{attrs:{label:"冰箱",value:"refrigerator"}}),t("el-option",{attrs:{label:"空调",value:"air_conditioner"}}),t("el-option",{attrs:{label:"电视",value:"television"}}),t("el-option",{attrs:{label:"微波炉",value:"microwave"}}),t("el-option",{attrs:{label:"热水器",value:"water_heater"}}),t("el-option",{attrs:{label:"油烟机",value:"range_hood"}}),t("el-option",{attrs:{label:"燃气灶",value:"gas_stove"}}),t("el-option",{attrs:{label:"其他",value:"other"}})],1)],1),t("el-col",{attrs:{span:4}},[t("el-select",{attrs:{placeholder:"紧急程度",clearable:""},on:{change:e.handleFilter},model:{value:e.filterUrgency,callback:function(t){e.filterUrgency=t},expression:"filterUrgency"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"一般",value:"low"}}),t("el-option",{attrs:{label:"紧急",value:"medium"}}),t("el-option",{attrs:{label:"非常紧急",value:"high"}})],1)],1),t("el-col",{attrs:{span:4}},[t("el-select",{attrs:{placeholder:"距离范围",clearable:""},on:{change:e.handleFilter},model:{value:e.filterDistance,callback:function(t){e.filterDistance=t},expression:"filterDistance"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"5公里内",value:"5"}}),t("el-option",{attrs:{label:"10公里内",value:"10"}}),t("el-option",{attrs:{label:"20公里内",value:"20"}})],1)],1),t("el-col",{attrs:{span:6}},[t("el-input",{attrs:{placeholder:"搜索工单号、品牌型号或地址","prefix-icon":"el-icon-search",clearable:""},on:{input:e.handleSearch},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}})],1),t("el-col",{attrs:{span:3}},[t("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.refreshOrders}},[e._v("刷新")])],1),t("el-col",{attrs:{span:3}},[t("el-switch",{attrs:{"active-text":"自动刷新"},on:{change:e.toggleAutoRefresh},model:{value:e.autoRefresh,callback:function(t){e.autoRefresh=t},expression:"autoRefresh"}})],1)],1)],1),t("div",{staticClass:"orders-container"},[e.loading?t("div",{staticClass:"loading-container"},[t("el-skeleton",{attrs:{rows:5,animated:""}})],1):0===e.filteredOrders.length?t("div",{staticClass:"empty-state"},[t("i",{staticClass:"el-icon-document"}),t("p",[e._v("暂无可接工单")]),t("el-button",{on:{click:e.refreshOrders}},[e._v("刷新试试")])],1):t("div",{staticClass:"orders-grid"},e._l(e.paginatedOrders,function(s){return t("el-card",{key:s.id,staticClass:"order-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"order-header"},[t("div",{staticClass:"order-id"},[e._v("#"+e._s(s.id))]),t("div",{staticClass:"order-urgency"},[t("el-tag",{attrs:{type:e.getUrgencyType(s.urgency),size:"small"}},[e._v(" "+e._s(e.getUrgencyText(s.urgency))+" ")])],1)]),t("div",{staticClass:"order-info"},[t("div",{staticClass:"appliance-info"},[t("h3",[e._v(e._s(e.getApplianceTypeName(s.appliance_type)))]),t("p",{staticClass:"brand"},[e._v(e._s(s.brand_model))])]),t("div",{staticClass:"problem-desc"},[t("p",[e._v(e._s(s.problem_description))])]),t("div",{staticClass:"location-info"},[t("div",{staticClass:"address"},[t("i",{staticClass:"el-icon-location"}),t("span",[e._v(e._s(s.address))])]),s.distance?t("div",{staticClass:"distance"},[t("i",{staticClass:"el-icon-position"}),t("span",[e._v(e._s(s.distance)+"km")])]):e._e()]),t("div",{staticClass:"time-info"},[t("div",{staticClass:"preferred-time"},[t("i",{staticClass:"el-icon-time"}),t("span",[e._v(e._s(e.formatDate(s.preferred_date))+" "+e._s(e.getTimeSlotText(s.preferred_time)))])]),t("div",{staticClass:"created-time"},[t("i",{staticClass:"el-icon-clock"}),t("span",[e._v(e._s(e.formatRelativeTime(s.created_at)))])])]),t("div",{staticClass:"customer-info"},[t("div",{staticClass:"contact"},[t("i",{staticClass:"el-icon-user"}),t("span",[e._v(e._s(s.contact_name))])]),t("div",{staticClass:"phone"},[t("i",{staticClass:"el-icon-phone"}),t("span",[e._v(e._s(s.contact_phone))])])])]),t("div",{staticClass:"order-actions"},[t("el-button",{attrs:{size:"small"},on:{click:function(t){return e.viewOrderDetail(s)}}},[e._v("查看详情")]),t("el-button",{attrs:{type:"primary",size:"small",loading:e.acceptingOrders.includes(s.id)},on:{click:function(t){return e.acceptOrder(s)}}},[e._v(" 接单 ")])],1)])}),1),e.filteredOrders.length>0?t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":[12,24,48],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.filteredOrders.length},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1):e._e()])],1)],1),t("el-dialog",{attrs:{visible:e.detailVisible,title:"工单详情",width:"800px","before-close":e.handleDetailClose},on:{"update:visible":function(t){e.detailVisible=t}}},[e.selectedOrder?t("order-detail",{attrs:{order:e.selectedOrder,"show-accept-button":!0},on:{accept:e.acceptOrder,refresh:e.refreshOrders}}):e._e()],1)],1)},We=[],Ke={name:"WorkerAvailableOrders",components:{OrderDetail:Ce},computed:{...(0,p.L8)(["currentUser"]),availableOrders(){return this.$store.state.availableOrders||[]},filteredOrders(){let e=this.availableOrders;if(this.filterType&&(e=e.filter(e=>e.appliance_type===this.filterType)),this.filterUrgency&&(e=e.filter(e=>e.urgency===this.filterUrgency)),this.filterDistance&&(e=e.filter(e=>e.distance&&e.distance<=parseInt(this.filterDistance))),this.searchKeyword){const t=this.searchKeyword.toLowerCase();e=e.filter(e=>e.id.toString().includes(t)||e.brand_model.toLowerCase().includes(t)||e.address.toLowerCase().includes(t))}return e},paginatedOrders(){const e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;return this.filteredOrders.slice(e,t)}},data(){return{loading:!1,detailVisible:!1,selectedOrder:null,filterType:"",filterUrgency:"",filterDistance:"",searchKeyword:"",currentPage:1,pageSize:12,autoRefresh:!1,refreshTimer:null,acceptingOrders:[]}},async created(){await this.refreshOrders()},beforeDestroy(){this.refreshTimer&&clearInterval(this.refreshTimer)},methods:{async refreshOrders(){this.loading=!0;try{await this.$store.dispatch("fetchAvailableOrders")}catch(e){console.error("[前端] 获取可接工单失败:",e),this.$message.error("获取可接工单失败")}finally{this.loading=!1}},handleFilter(){this.currentPage=1},handleSearch(){this.currentPage=1},handleSizeChange(e){this.pageSize=e,this.currentPage=1},handleCurrentChange(e){this.currentPage=e},toggleAutoRefresh(e){e?(this.refreshTimer=setInterval(()=>{this.refreshOrders()},3e4),this.$message.success("已开启自动刷新")):(this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null),this.$message.info("已关闭自动刷新"))},viewOrderDetail(e){this.selectedOrder=e,this.detailVisible=!0},handleDetailClose(){this.detailVisible=!1,this.selectedOrder=null},async acceptOrder(e){try{this.acceptingOrders.push(e.id),await this.$confirm(`确定要接受工单 #${e.id} 吗？`,"确认接单",{confirmButtonText:"确定接单",cancelButtonText:"取消",type:"info"});const t=await this.$store.dispatch("acceptOrder",e.id);t.success?(this.$message.success("接单成功！"),this.detailVisible=!1,await this.refreshOrders()):this.$message.error(t.message||"接单失败")}catch(t){"cancel"!==t&&(console.error("[前端] 接单失败:",t),this.$message.error("接单失败，请重试"))}finally{const t=this.acceptingOrders.indexOf(e.id);t>-1&&this.acceptingOrders.splice(t,1)}},getApplianceTypeName(e){const t={washing_machine:"洗衣机",refrigerator:"冰箱",air_conditioner:"空调",television:"电视",microwave:"微波炉",water_heater:"热水器",range_hood:"油烟机",gas_stove:"燃气灶",other:"其他"};return t[e]||e},getUrgencyType(e){const t={low:"info",medium:"warning",high:"danger"};return t[e]||"info"},getUrgencyText(e){const t={low:"一般",medium:"紧急",high:"非常紧急"};return t[e]||"一般"},getTimeSlotText(e){const t={morning:"上午 (9:00-12:00)",afternoon:"下午 (13:00-17:00)",evening:"晚上 (18:00-20:00)"};return t[e]||""},formatDate(e){if(!e)return"-";const t=new Date(e);return t.toLocaleDateString("zh-CN")},formatRelativeTime(e){if(!e)return"-";const t=new Date,s=new Date(e),a=t-s,r=Math.floor(a/6e4),i=Math.floor(r/60),l=Math.floor(i/24);return l>0?`${l}天前`:i>0?`${i}小时前`:r>0?`${r}分钟前`:"刚刚"}}},He=Ke,Qe=(0,n.A)(He,je,We,!1,null,"34e96871",null),Je=Qe.exports,Ye=function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-container admin-theme"},[t("div",{staticClass:"login-form"},[t("div",{staticClass:"login-header"},[t("div",{staticClass:"back-btn"},[t("el-button",{attrs:{icon:"el-icon-arrow-left"},on:{click:function(t){return e.$router.push("/")}}},[e._v("返回首页")])],1),t("h2",[e._v("管理员登录")]),t("p",[e._v("永盛制冷维修有限公司-家电售卖与维修")])]),t("el-form",{ref:"loginForm",attrs:{model:e.loginForm,rules:e.loginRules,"label-width":"0"},nativeOn:{submit:function(t){return t.preventDefault(),e.handleLogin.apply(null,arguments)}}},[t("el-form-item",{attrs:{prop:"username"}},[t("el-input",{attrs:{placeholder:"请输入用户名","prefix-icon":"el-icon-user",size:"large"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}})],1),t("el-form-item",{attrs:{prop:"password"}},[t("el-input",{attrs:{type:"password",placeholder:"请输入密码","prefix-icon":"el-icon-lock",size:"large","show-password":""},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}})],1),t("el-form-item",[t("el-button",{staticStyle:{width:"100%"},attrs:{type:"warning",size:"large",loading:e.$store.state.loading},on:{click:e.handleLogin}},[e._v(" 登录 ")])],1)],1)],1)])},Xe=[],Ze={name:"AdminLogin",data(){return{loginForm:{username:"",password:""},loginRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]}}},methods:{async handleLogin(){try{await this.$refs.loginForm.validate();const e=await this.$store.dispatch("login",{credentials:this.loginForm,userType:"admin"});e.success?(this.$message.success("登录成功"),this.$router.push("/admin/home")):this.$message.error(e.message)}catch(e){console.error("登录失败:",e)}}}},et=Ze,tt=(0,n.A)(et,Ye,Xe,!1,null,"729cceb0",null),st=tt.exports,at=function(){var e=this,t=e._self._c;return t("div",{staticClass:"admin-home"},[t("el-container",[t("el-header",{staticClass:"admin-header"},[t("div",{staticClass:"mobile-menu-btn",on:{click:e.toggleMobileMenu}},[e.mobileMenuOpen?t("i",{staticClass:"el-icon-s-fold"}):t("i",{staticClass:"el-icon-s-unfold"})]),t("div",{staticClass:"header-left"},[t("h2",[e._v("永盛制冷维修有限公司-管理系统")])]),t("div",{staticClass:"header-right"},[t("el-dropdown",{on:{command:e.handleCommand}},[t("span",{staticClass:"user-info"},[t("i",{staticClass:"el-icon-user-solid"}),t("span",{staticClass:"user-name"},[e._v(e._s(e.currentUser?.name||"管理员"))]),t("i",{staticClass:"el-icon-arrow-down"})]),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{attrs:{command:"logout"}},[e._v("退出登录")])],1)],1)],1)]),e.mobileMenuOpen?t("div",{staticClass:"mobile-overlay",on:{click:e.closeMobileMenu}}):e._e(),t("el-container",[t("el-aside",{staticClass:"admin-sidebar",class:{"mobile-open":e.mobileMenuOpen},attrs:{width:"200px"}},[t("el-menu",{attrs:{"default-active":e.$route.path,router:"","background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409EFF"},on:{select:e.handleMenuClick}},[t("el-menu-item",{attrs:{index:"/admin/home"}},[t("i",{staticClass:"el-icon-s-home"}),t("span",[e._v("首页")])]),t("el-menu-item",{attrs:{index:"/admin/orders"}},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("工单管理")])]),t("el-menu-item",{attrs:{index:"/admin/users"}},[t("i",{staticClass:"el-icon-user"}),t("span",[e._v("用户管理")])]),t("el-menu-item",{attrs:{index:"/admin/statistics"}},[t("i",{staticClass:"el-icon-s-data"}),t("span",[e._v("统计报表")])]),t("el-menu-item",{attrs:{index:"/admin/salary"}},[t("i",{staticClass:"el-icon-money"}),t("span",[e._v("薪资管理")])])],1)],1),t("el-main",{staticClass:"admin-main"},[t("div",{staticClass:"welcome-section"},[t("div",{staticClass:"welcome-content"},[t("h1",[e._v("管理控制台")]),t("p",[e._v("欢迎回来，"+e._s(e.currentUser?.name||"管理员")+"！系统运行正常")]),t("div",{staticClass:"welcome-decoration"})])]),t("div",{staticClass:"section-title"},[t("h2",[e._v("工单统计")])]),t("div",{staticClass:"stats-grid"},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon total"},[t("i",{staticClass:"el-icon-document"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.statistics.orders?.total_orders||0))]),t("div",{staticClass:"stat-label"},[e._v("总工单数")])])]),t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon pending"},[t("i",{staticClass:"el-icon-time"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.statistics.orders?.pending_orders||0))]),t("div",{staticClass:"stat-label"},[e._v("待处理工单")])])]),t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon processing"},[t("i",{staticClass:"el-icon-loading"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.statistics.orders?.accepted_orders||0))]),t("div",{staticClass:"stat-label"},[e._v("处理中工单")])])]),t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon completed"},[t("i",{staticClass:"el-icon-check"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.statistics.orders?.completed_orders||0))]),t("div",{staticClass:"stat-label"},[e._v("已完成工单")])])])]),t("div",{staticClass:"section-title"},[t("h2",[e._v("用户统计")])]),t("div",{staticClass:"stats-grid user-stats"},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon customers"},[t("i",{staticClass:"el-icon-user"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.statistics.users?.total_customers||0))]),t("div",{staticClass:"stat-label"},[e._v("注册客户")])])]),t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon workers"},[t("i",{staticClass:"el-icon-s-tools"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.statistics.users?.total_workers||0))]),t("div",{staticClass:"stat-label"},[e._v("在职工人")])])])]),t("div",{staticClass:"section-title"},[t("h2",[e._v("快速操作")])]),t("div",{staticClass:"quick-actions"},[t("div",{staticClass:"action-grid"},[t("div",{staticClass:"action-card",on:{click:function(t){return e.$router.push("/admin/orders")}}},[t("div",{staticClass:"action-icon"},[t("i",{staticClass:"el-icon-document"})]),t("h3",[e._v("管理工单")]),t("p",[e._v("查看和管理所有工单")])]),t("div",{staticClass:"action-card",on:{click:function(t){return e.$router.push("/admin/users")}}},[t("div",{staticClass:"action-icon"},[t("i",{staticClass:"el-icon-user"})]),t("h3",[e._v("管理用户")]),t("p",[e._v("管理客户和工人账户")])]),t("div",{staticClass:"action-card",on:{click:function(t){return e.$router.push("/admin/statistics")}}},[t("div",{staticClass:"action-icon"},[t("i",{staticClass:"el-icon-s-data"})]),t("h3",[e._v("查看报表")]),t("p",[e._v("查看详细统计报表")])]),t("div",{staticClass:"action-card",on:{click:function(t){return e.$router.push("/admin/salary")}}},[t("div",{staticClass:"action-icon"},[t("i",{staticClass:"el-icon-money"})]),t("h3",[e._v("薪资管理")]),t("p",[e._v("管理工人薪资结算")])])])]),t("el-card",{staticClass:"recent-orders",attrs:{header:"最近工单"}},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.recentOrders}},[t("el-table-column",{attrs:{prop:"id",label:"工单号",width:"80"}}),t("el-table-column",{attrs:{prop:"customer_name",label:"客户",width:"100"}}),t("el-table-column",{attrs:{prop:"appliance_type",label:"设备类型",width:"120"}}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusType(s.row.status)}},[e._v(" "+e._s(e.getStatusText(s.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"created_at",label:"创建时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDate(t.row.created_at))+" ")]}}])})],1)],1)],1)],1)],1)],1)},rt=[],it={name:"AdminHome",data(){return{statistics:{orders:{},users:{}},recentOrders:[],mobileMenuOpen:!1}},computed:{currentUser(){return this.$store.state.user}},async created(){await this.loadStatistics(),await this.loadRecentOrders()},mounted(){window.addEventListener("resize",this.handleResize)},beforeDestroy(){window.removeEventListener("resize",this.handleResize)},methods:{async loadStatistics(){try{const e=await this.$store.dispatch("fetchStatistics");e.success&&(this.statistics=this.$store.state.statistics||{})}catch(e){console.error("加载统计数据失败:",e)}},async loadRecentOrders(){try{const e=await this.$store.dispatch("fetchAllOrders");e.success&&(this.recentOrders=(this.$store.state.orders||[]).slice(0,5))}catch(e){console.error("加载最近工单失败:",e)}},handleCommand(e){"logout"===e&&this.logout()},async logout(){try{await this.$confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await this.$store.dispatch("logout");e.success&&(this.$message.success("退出登录成功"),this.$router.push("/"))}catch(e){console.log("用户取消退出登录")}},toggleMobileMenu(){this.mobileMenuOpen=!this.mobileMenuOpen},closeMobileMenu(){this.mobileMenuOpen=!1},handleMenuClick(){window.innerWidth<=768&&this.closeMobileMenu()},handleResize(){window.innerWidth>768&&this.mobileMenuOpen&&this.closeMobileMenu()},getStatusType(e){const t={pending:"warning",accepted:"info",in_progress:"primary",completed:"success",cancelled:"danger"};return t[e]||"info"},getStatusText(e){const t={pending:"待接单",accepted:"已接单",in_progress:"维修中",completed:"已完成",cancelled:"已取消"};return t[e]||e},formatDate(e){if(!e)return"";const t=new Date(e);return t.toLocaleString("zh-CN")}}},lt=it,ot=(0,n.A)(lt,at,rt,!1,null,"f7d72052",null),nt=ot.exports,ct=function(){var e=this,t=e._self._c;return t("div",{staticClass:"admin-orders"},[t("el-container",[t("el-header",{staticClass:"admin-header"},[t("div",{staticClass:"header-left"},[t("h2",[e._v("工单管理")])]),t("div",{staticClass:"header-right"},[t("el-button",{on:{click:function(t){return e.$router.push("/admin/home")}}},[e._v("返回首页")])],1)]),t("el-main",{staticClass:"admin-main"},[t("el-card",{staticClass:"search-card",attrs:{shadow:"never"}},[t("el-form",{staticClass:"search-form",attrs:{inline:!0,model:e.searchForm}},[t("el-form-item",{attrs:{label:"工单状态"}},[t("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.searchForm.status,callback:function(t){e.$set(e.searchForm,"status",t)},expression:"searchForm.status"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"待接单",value:"pending"}}),t("el-option",{attrs:{label:"已接单",value:"accepted"}}),t("el-option",{attrs:{label:"维修中",value:"in_progress"}}),t("el-option",{attrs:{label:"已完成",value:"completed"}}),t("el-option",{attrs:{label:"已取消",value:"cancelled"}})],1)],1),t("el-form-item",{attrs:{label:"设备类型"}},[t("el-select",{attrs:{placeholder:"请选择设备类型",clearable:""},model:{value:e.searchForm.appliance_type,callback:function(t){e.$set(e.searchForm,"appliance_type",t)},expression:"searchForm.appliance_type"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"空调",value:"air_conditioner"}}),t("el-option",{attrs:{label:"洗衣机",value:"washing_machine"}}),t("el-option",{attrs:{label:"冰箱",value:"refrigerator"}}),t("el-option",{attrs:{label:"热水器",value:"water_heater"}}),t("el-option",{attrs:{label:"电视",value:"television"}}),t("el-option",{attrs:{label:"微波炉",value:"microwave"}})],1)],1),t("el-form-item",{attrs:{label:"客户姓名"}},[t("el-input",{attrs:{placeholder:"请输入客户姓名",clearable:""},model:{value:e.searchForm.customer_name,callback:function(t){e.$set(e.searchForm,"customer_name",t)},expression:"searchForm.customer_name"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.searchOrders}},[e._v("搜索")]),t("el-button",{on:{click:e.resetSearch}},[e._v("重置")])],1)],1)],1),t("el-card",{staticClass:"orders-card"},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("工单列表 (共 "+e._s(e.filteredOrders.length)+" 条)")]),t("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.refreshOrders}},[e._v("刷新")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.$store.state.loading,expression:"$store.state.loading"}],staticStyle:{width:"100%"},attrs:{data:e.paginatedOrders},on:{"row-click":e.showOrderDetail}},[t("el-table-column",{attrs:{prop:"id",label:"工单号",width:"80",sortable:""}}),t("el-table-column",{attrs:{prop:"customer_name",label:"客户",width:"100"}}),t("el-table-column",{attrs:{prop:"customer_phone",label:"客户电话",width:"120"}}),t("el-table-column",{attrs:{prop:"appliance_type",label:"设备类型",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getApplianceTypeName(t.row.appliance_type))+" ")]}}])}),t("el-table-column",{attrs:{prop:"brand_model",label:"品牌型号",width:"150","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"worker_name",label:"维修工人",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.worker_name||"未分配")+" ")]}}])}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusType(s.row.status)}},[e._v(" "+e._s(e.getStatusText(s.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"urgency",label:"紧急程度",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getUrgencyType(s.row.urgency),size:"small"}},[e._v(" "+e._s(e.getUrgencyText(s.row.urgency))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDate(t.row.created_at))+" ")]}}])}),t("el-table-column",{attrs:{label:"操作",width:"120",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return t.stopPropagation(),e.showOrderDetail(s.row)}}},[e._v(" 查看详情 ")])]}}])})],1),t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.filteredOrders.length},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)],1),t("el-dialog",{attrs:{title:"工单详情",visible:e.detailVisible,width:"800px"},on:{"update:visible":function(t){e.detailVisible=t},close:function(t){e.selectedOrder=null}}},[e.selectedOrder?t("div",{staticClass:"order-detail"},[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"工单号"}},[e._v(e._s(e.selectedOrder.id))]),t("el-descriptions-item",{attrs:{label:"状态"}},[t("el-tag",{attrs:{type:e.getStatusType(e.selectedOrder.status)}},[e._v(" "+e._s(e.getStatusText(e.selectedOrder.status))+" ")])],1),t("el-descriptions-item",{attrs:{label:"客户姓名"}},[e._v(e._s(e.selectedOrder.customer_name))]),t("el-descriptions-item",{attrs:{label:"客户电话"}},[e._v(e._s(e.selectedOrder.customer_phone))]),t("el-descriptions-item",{attrs:{label:"设备类型"}},[e._v(e._s(e.getApplianceTypeName(e.selectedOrder.appliance_type)))]),t("el-descriptions-item",{attrs:{label:"品牌型号"}},[e._v(e._s(e.selectedOrder.brand_model))]),t("el-descriptions-item",{attrs:{label:"维修工人"}},[e._v(e._s(e.selectedOrder.worker_name||"未分配"))]),t("el-descriptions-item",{attrs:{label:"工人电话"}},[e._v(e._s(e.selectedOrder.worker_phone||"未分配"))]),t("el-descriptions-item",{attrs:{label:"紧急程度"}},[t("el-tag",{attrs:{type:e.getUrgencyType(e.selectedOrder.urgency),size:"small"}},[e._v(" "+e._s(e.getUrgencyText(e.selectedOrder.urgency))+" ")])],1),t("el-descriptions-item",{attrs:{label:"预约日期"}},[e._v(e._s(e.selectedOrder.preferred_date))]),t("el-descriptions-item",{attrs:{label:"预约时间"}},[e._v(e._s(e.getTimeText(e.selectedOrder.preferred_time)))]),t("el-descriptions-item",{attrs:{label:"购买日期"}},[e._v(e._s(e.selectedOrder.purchase_date))]),t("el-descriptions-item",{attrs:{label:"创建时间"}},[e._v(e._s(e.formatDate(e.selectedOrder.created_at)))]),t("el-descriptions-item",{attrs:{label:"接单时间"}},[e._v(e._s(e.formatDate(e.selectedOrder.accepted_at)))]),t("el-descriptions-item",{attrs:{label:"完成时间"}},[e._v(e._s(e.formatDate(e.selectedOrder.completed_at)))]),t("el-descriptions-item",{attrs:{label:"评分"}},[e.selectedOrder.rating?t("el-rate",{attrs:{value:e.selectedOrder.rating,disabled:""}}):t("span",[e._v("未评分")])],1)],1),t("div",{staticClass:"detail-section"},[t("h4",[e._v("故障描述")]),t("p",[e._v(e._s(e.selectedOrder.problem_description))])]),e.selectedOrder.address?t("div",{staticClass:"detail-section"},[t("h4",[e._v("服务地址")]),t("p",[e._v(e._s(e.selectedOrder.address))])]):e._e(),e.selectedOrder.remarks?t("div",{staticClass:"detail-section"},[t("h4",[e._v("备注信息")]),t("p",[e._v(e._s(e.selectedOrder.remarks))])]):e._e(),e.selectedOrder.comment?t("div",{staticClass:"detail-section"},[t("h4",[e._v("客户评价")]),t("p",[e._v(e._s(e.selectedOrder.comment))])]):e._e()],1):e._e()])],1)},dt=[],ut={name:"AdminOrders",data(){return{orders:[],searchForm:{status:"",appliance_type:"",customer_name:""},currentPage:1,pageSize:20,detailVisible:!1,selectedOrder:null}},computed:{filteredOrders(){let e=this.orders;return this.searchForm.status&&(e=e.filter(e=>e.status===this.searchForm.status)),this.searchForm.appliance_type&&(e=e.filter(e=>e.appliance_type===this.searchForm.appliance_type)),this.searchForm.customer_name&&(e=e.filter(e=>e.customer_name.includes(this.searchForm.customer_name))),e},paginatedOrders(){const e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;return this.filteredOrders.slice(e,t)}},async created(){await this.loadOrders()},methods:{async loadOrders(){try{const e=await this.$store.dispatch("fetchAllOrders");e.success?this.orders=this.$store.state.orders||[]:this.$message.error(e.message||"获取工单列表失败")}catch(e){console.error("加载工单失败:",e),this.$message.error("加载工单失败")}},async refreshOrders(){await this.loadOrders(),this.$message.success("刷新成功")},searchOrders(){this.currentPage=1},resetSearch(){this.searchForm={status:"",appliance_type:"",customer_name:""},this.currentPage=1},showOrderDetail(e){this.selectedOrder=e,this.detailVisible=!0},handleSizeChange(e){this.pageSize=e,this.currentPage=1},handleCurrentChange(e){this.currentPage=e},getApplianceTypeName(e){const t={air_conditioner:"空调",washing_machine:"洗衣机",refrigerator:"冰箱",water_heater:"热水器",television:"电视",microwave:"微波炉"};return t[e]||e},getStatusType(e){const t={pending:"warning",accepted:"info",in_progress:"primary",completed:"success",cancelled:"danger"};return t[e]||"info"},getStatusText(e){const t={pending:"待接单",accepted:"已接单",in_progress:"维修中",completed:"已完成",cancelled:"已取消"};return t[e]||e},getUrgencyType(e){const t={low:"info",medium:"warning",high:"danger"};return t[e]||"info"},getUrgencyText(e){const t={low:"低",medium:"中",high:"高"};return t[e]||e},getTimeText(e){const t={morning:"上午",afternoon:"下午",evening:"晚上"};return t[e]||e},formatDate(e){if(!e)return"未设置";const t=new Date(e);return t.toLocaleString("zh-CN")}}},pt=ut,mt=(0,n.A)(pt,ct,dt,!1,null,"028b86d9",null),ht=mt.exports,gt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"admin-statistics"},[t("el-container",[t("el-header",{staticClass:"admin-header"},[t("div",{staticClass:"header-left"},[t("h2",[e._v("统计报表")])]),t("div",{staticClass:"header-right"},[t("el-button",{on:{click:function(t){return e.$router.push("/admin/home")}}},[e._v("返回首页")])],1)]),t("el-main",{staticClass:"admin-main"},[t("el-card",{staticClass:"filter-card",attrs:{shadow:"never"}},[t("el-form",{staticClass:"filter-form",attrs:{inline:!0,model:e.filterForm}},[t("el-form-item",{attrs:{label:"时间范围"}},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},on:{change:e.handleDateChange},model:{value:e.filterForm.dateRange,callback:function(t){e.$set(e.filterForm,"dateRange",t)},expression:"filterForm.dateRange"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadStatistics}},[e._v("查询")]),t("el-button",{on:{click:e.resetFilter}},[e._v("重置")]),t("el-button",{attrs:{type:"success"},on:{click:e.exportReport}},[e._v("导出报表")])],1)],1)],1),t("div",{staticClass:"stats-overview"},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon total"},[t("i",{staticClass:"el-icon-document"})]),t("div",{staticClass:"stat-content"},[t("h3",[e._v(e._s(e.statistics.orders?.total_orders||0))]),t("p",[e._v("总工单数")])])]),t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon completed"},[t("i",{staticClass:"el-icon-check"})]),t("div",{staticClass:"stat-content"},[t("h3",[e._v(e._s(e.statistics.orders?.completed_orders||0))]),t("p",[e._v("已完成工单")])])]),t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon revenue"},[t("i",{staticClass:"el-icon-money"})]),t("div",{staticClass:"stat-content"},[t("h3",[e._v("¥"+e._s(e.statistics.revenue?.total_revenue||0))]),t("p",[e._v("总收入")])])]),t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon customers"},[t("i",{staticClass:"el-icon-user"})]),t("div",{staticClass:"stat-content"},[t("h3",[e._v(e._s(e.statistics.users?.total_customers||0))]),t("p",[e._v("注册客户")])])])]),t("div",{staticClass:"charts-container"},[t("el-card",{staticClass:"chart-card",attrs:{header:"工单状态分布"}},[t("div",{staticClass:"chart-placeholder"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.orderStatusData}},[t("el-table-column",{attrs:{prop:"status",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusType(s.row.status)}},[e._v(" "+e._s(e.getStatusText(s.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"count",label:"数量",sortable:""}}),t("el-table-column",{attrs:{prop:"percentage",label:"占比"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.percentage)+"% ")]}}])})],1)],1)]),t("el-card",{staticClass:"chart-card",attrs:{header:"设备类型统计"}},[t("div",{staticClass:"chart-placeholder"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.applianceTypeData}},[t("el-table-column",{attrs:{prop:"type",label:"设备类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getApplianceTypeName(t.row.type))+" ")]}}])}),t("el-table-column",{attrs:{prop:"count",label:"维修次数",sortable:""}}),t("el-table-column",{attrs:{prop:"percentage",label:"占比"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.percentage)+"% ")]}}])})],1)],1)])],1),t("el-card",{staticClass:"performance-card",attrs:{header:"工人绩效统计"}},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.workerPerformance}},[t("el-table-column",{attrs:{prop:"name",label:"工人姓名",width:"100"}}),t("el-table-column",{attrs:{prop:"total_orders",label:"总工单数",width:"100",sortable:""}}),t("el-table-column",{attrs:{prop:"completed_orders",label:"已完成",width:"100",sortable:""}}),t("el-table-column",{attrs:{prop:"completion_rate",label:"完成率",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-progress",{attrs:{percentage:s.row.completion_rate,color:e.getProgressColor(s.row.completion_rate)}})]}}])}),t("el-table-column",{attrs:{prop:"avg_rating",label:"平均评分",width:"120"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("el-rate",{attrs:{value:parseFloat(e.row.avg_rating)||0,disabled:"","show-score":""}})]}}])}),t("el-table-column",{attrs:{prop:"total_revenue",label:"总收入",width:"100",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(t.row.total_revenue||0)+" ")]}}])}),t("el-table-column",{attrs:{prop:"commission",label:"提成",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(t.row.commission||0)+" ")]}}])})],1)],1),t("el-card",{staticClass:"trend-card",attrs:{header:"月度工单趋势"}},[t("div",{staticClass:"trend-placeholder"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.monthlyTrend}},[t("el-table-column",{attrs:{prop:"month",label:"月份",width:"100"}}),t("el-table-column",{attrs:{prop:"total_orders",label:"总工单",width:"100"}}),t("el-table-column",{attrs:{prop:"completed_orders",label:"已完成",width:"100"}}),t("el-table-column",{attrs:{prop:"revenue",label:"收入",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(t.row.revenue||0)+" ")]}}])}),t("el-table-column",{attrs:{prop:"growth_rate",label:"增长率",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("span",{class:s.row.growth_rate>=0?"positive":"negative"},[e._v(" "+e._s(s.row.growth_rate>=0?"+":"")+e._s(s.row.growth_rate)+"% ")])]}}])})],1)],1)])],1)],1)],1)},vt=[],_t={name:"AdminStatistics",data(){return{filterForm:{dateRange:[]},statistics:{orders:{},users:{},revenue:{}},orderStatusData:[],applianceTypeData:[],workerPerformance:[],monthlyTrend:[]}},async created(){await this.loadStatistics()},methods:{async loadStatistics(){try{const e=await this.$store.dispatch("fetchStatistics");e.success&&(this.statistics=this.$store.state.statistics||{}),await this.loadDetailedStats()}catch(e){console.error("加载统计数据失败:",e),this.$message.error("加载统计数据失败")}},async loadDetailedStats(){try{this.loading=!0,await this.loadOrderStatusData(),await this.loadApplianceTypeData(),await this.loadWorkerPerformanceData(),await this.loadMonthlyTrendData()}catch(e){console.error("加载详细统计失败:",e),this.$message.error("加载统计数据失败")}finally{this.loading=!1}},async loadOrderStatusData(){try{const e=await this.$store.dispatch("fetchOrderStatusStats");if(e.success){const t=e.data,s=t.total||0;this.orderStatusData=[{status:"pending",count:t.pending||0,percentage:s?Math.round((t.pending||0)/s*100):0},{status:"accepted",count:t.accepted||0,percentage:s?Math.round((t.accepted||0)/s*100):0},{status:"in_progress",count:t.in_progress||0,percentage:s?Math.round((t.in_progress||0)/s*100):0},{status:"completed",count:t.completed||0,percentage:s?Math.round((t.completed||0)/s*100):0},{status:"cancelled",count:t.cancelled||0,percentage:s?Math.round((t.cancelled||0)/s*100):0}]}}catch(e){console.error("获取工单状态统计失败:",e)}},async loadApplianceTypeData(){try{const e=await this.$store.dispatch("fetchApplianceTypeStats");e.success&&(this.applianceTypeData=e.data||[])}catch(e){console.error("获取设备类型统计失败:",e)}},async loadWorkerPerformanceData(){try{const e=await this.$store.dispatch("fetchWorkerPerformanceStats");e.success&&(this.workerPerformance=e.data||[])}catch(e){console.error("获取工人绩效统计失败:",e)}},async loadMonthlyTrendData(){try{const e=await this.$store.dispatch("fetchMonthlyTrendStats");e.success&&(this.monthlyTrend=e.data||[])}catch(e){console.error("获取月度趋势统计失败:",e)}},handleDateChange(){this.loadStatistics()},resetFilter(){this.filterForm.dateRange=[],this.loadStatistics()},exportReport(){this.$message.info("导出功能开发中...")},getStatusType(e){const t={pending:"warning",accepted:"info",in_progress:"primary",completed:"success",cancelled:"danger"};return t[e]||"info"},getStatusText(e){const t={pending:"待接单",accepted:"已接单",in_progress:"维修中",completed:"已完成",cancelled:"已取消"};return t[e]||e},getApplianceTypeName(e){const t={air_conditioner:"空调",washing_machine:"洗衣机",refrigerator:"冰箱",water_heater:"热水器",television:"电视",microwave:"微波炉"};return t[e]||e},getProgressColor(e){return e>=90?"#67c23a":e>=70?"#e6a23c":"#f56c6c"}}},ft=_t,bt=(0,n.A)(ft,gt,vt,!1,null,"0de6316e",null),yt=bt.exports,wt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"admin-users"},[t("el-container",[t("el-header",{staticClass:"admin-header"},[t("div",{staticClass:"header-left"},[t("h2",[e._v("用户管理")])]),t("div",{staticClass:"header-right"},[t("el-button",{on:{click:function(t){return e.$router.push("/admin/home")}}},[e._v("返回首页")])],1)]),t("el-main",{staticClass:"admin-main"},[t("el-card",{staticClass:"user-type-card",attrs:{shadow:"never"}},[t("el-radio-group",{on:{change:e.handleUserTypeChange},model:{value:e.activeUserType,callback:function(t){e.activeUserType=t},expression:"activeUserType"}},[t("el-radio-button",{attrs:{label:"customers"}},[e._v("客户管理")]),t("el-radio-button",{attrs:{label:"workers"}},[e._v("工人管理")]),t("el-radio-button",{attrs:{label:"admins"}},[e._v("管理员管理")])],1)],1),t("el-card",{staticClass:"search-card",attrs:{shadow:"never"}},[t("el-form",{staticClass:"search-form",attrs:{inline:!0,model:e.searchForm}},[t("el-form-item",{attrs:{label:"用户名"}},[t("el-input",{attrs:{placeholder:"请输入用户名",clearable:""},model:{value:e.searchForm.username,callback:function(t){e.$set(e.searchForm,"username",t)},expression:"searchForm.username"}})],1),t("el-form-item",{attrs:{label:"姓名"}},[t("el-input",{attrs:{placeholder:"请输入姓名",clearable:""},model:{value:e.searchForm.name,callback:function(t){e.$set(e.searchForm,"name",t)},expression:"searchForm.name"}})],1),"workers"===e.activeUserType?t("el-form-item",{attrs:{label:"状态"}},[t("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.searchForm.status,callback:function(t){e.$set(e.searchForm,"status",t)},expression:"searchForm.status"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"在职",value:"active"}}),t("el-option",{attrs:{label:"离职",value:"inactive"}})],1)],1):e._e(),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.searchUsers}},[e._v("搜索")]),t("el-button",{on:{click:e.resetSearch}},[e._v("重置")]),"workers"===e.activeUserType?t("el-button",{attrs:{type:"success"},on:{click:e.showAddDialog}},[e._v("添加工人")]):e._e()],1)],1)],1),t("el-card",{staticClass:"users-card"},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(e.getUserTypeText())+"列表 (共 "+e._s(e.filteredUsers.length)+" 条)")]),t("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.refreshUsers}},[e._v("刷新")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.paginatedUsers}},[t("el-table-column",{attrs:{prop:"id",label:"ID",width:"60",sortable:""}}),t("el-table-column",{attrs:{prop:"username",label:"用户名",width:"120"}}),t("el-table-column",{attrs:{prop:"name",label:"姓名",width:"100"}}),t("el-table-column",{attrs:{prop:"phone",label:"电话",width:"130"}}),"customers"===e.activeUserType?t("el-table-column",{attrs:{prop:"address",label:"地址","show-overflow-tooltip":""}}):e._e(),"workers"===e.activeUserType?[t("el-table-column",{attrs:{prop:"skills",label:"技能",width:"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"commission_rate",label:"提成比例",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s((100*t.row.commission_rate).toFixed(0))+"% ")]}}],null,!1,289654272)}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"80"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:"active"===s.row.status?"success":"danger"}},[e._v(" "+e._s("active"===s.row.status?"在职":"离职")+" ")])]}}],null,!1,1675924931)})]:e._e(),"admins"===e.activeUserType?[t("el-table-column",{attrs:{prop:"role",label:"角色",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:"super_admin"===s.row.role?"danger":"primary"}},[e._v(" "+e._s("super_admin"===s.row.role?"超级管理员":"管理员")+" ")])]}}],null,!1,3528491741)}),t("el-table-column",{attrs:{prop:"last_login",label:"最后登录",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDate(t.row.last_login))+" ")]}}],null,!1,794869117)})]:e._e(),t("el-table-column",{attrs:{prop:"created_at",label:"注册时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDate(t.row.created_at))+" ")]}}])}),t("el-table-column",{attrs:{label:"操作",width:"200",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showUserDetail(s.row)}}},[e._v(" 查看详情 ")]),"workers"===e.activeUserType?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editUser(s.row)}}},[e._v(" 编辑 ")]):e._e(),"workers"===e.activeUserType?t("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteUser(s.row)}}},[e._v(" 删除 ")]):e._e()]}}])})],2),t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.filteredUsers.length},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)],1),t("el-dialog",{attrs:{title:"用户详情",visible:e.detailVisible,width:"600px"},on:{"update:visible":function(t){e.detailVisible=t},close:function(t){e.selectedUser=null}}},[e.selectedUser?t("div",{staticClass:"user-detail"},[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"ID"}},[e._v(e._s(e.selectedUser.id))]),t("el-descriptions-item",{attrs:{label:"用户名"}},[e._v(e._s(e.selectedUser.username))]),t("el-descriptions-item",{attrs:{label:"姓名"}},[e._v(e._s(e.selectedUser.name))]),t("el-descriptions-item",{attrs:{label:"电话"}},[e._v(e._s(e.selectedUser.phone))]),"customers"===e.activeUserType?t("el-descriptions-item",{attrs:{label:"地址",span:2}},[e._v(" "+e._s(e.selectedUser.address)+" ")]):e._e(),"workers"===e.activeUserType?[t("el-descriptions-item",{attrs:{label:"技能",span:2}},[e._v(e._s(e.selectedUser.skills))]),t("el-descriptions-item",{attrs:{label:"提成比例"}},[e._v(e._s((100*e.selectedUser.commission_rate).toFixed(0))+"%")]),t("el-descriptions-item",{attrs:{label:"状态"}},[t("el-tag",{attrs:{type:"active"===e.selectedUser.status?"success":"danger"}},[e._v(" "+e._s("active"===e.selectedUser.status?"在职":"离职")+" ")])],1)]:e._e(),"admins"===e.activeUserType?[t("el-descriptions-item",{attrs:{label:"角色"}},[t("el-tag",{attrs:{type:"super_admin"===e.selectedUser.role?"danger":"primary"}},[e._v(" "+e._s("super_admin"===e.selectedUser.role?"超级管理员":"管理员")+" ")])],1),t("el-descriptions-item",{attrs:{label:"最后登录"}},[e._v(e._s(e.formatDate(e.selectedUser.last_login)))])]:e._e(),t("el-descriptions-item",{attrs:{label:"注册时间"}},[e._v(e._s(e.formatDate(e.selectedUser.created_at)))])],2)],1):e._e()]),t("el-dialog",{attrs:{title:e.editMode?"编辑工人":"添加工人",visible:e.editVisible,width:"500px"},on:{"update:visible":function(t){e.editVisible=t},close:e.resetEditForm}},[t("el-form",{ref:"editForm",attrs:{model:e.editForm,rules:e.editRules,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"用户名",prop:"username"}},[t("el-input",{attrs:{disabled:e.editMode},model:{value:e.editForm.username,callback:function(t){e.$set(e.editForm,"username",t)},expression:"editForm.username"}})],1),e.editMode?e._e():t("el-form-item",{attrs:{label:"密码",prop:"password"}},[t("el-input",{attrs:{type:"password"},model:{value:e.editForm.password,callback:function(t){e.$set(e.editForm,"password",t)},expression:"editForm.password"}})],1),t("el-form-item",{attrs:{label:"姓名",prop:"name"}},[t("el-input",{model:{value:e.editForm.name,callback:function(t){e.$set(e.editForm,"name",t)},expression:"editForm.name"}})],1),t("el-form-item",{attrs:{label:"电话",prop:"phone"}},[t("el-input",{model:{value:e.editForm.phone,callback:function(t){e.$set(e.editForm,"phone",t)},expression:"editForm.phone"}})],1),t("el-form-item",{attrs:{label:"技能",prop:"skills"}},[t("el-input",{attrs:{placeholder:"请输入技能，用逗号分隔"},model:{value:e.editForm.skills,callback:function(t){e.$set(e.editForm,"skills",t)},expression:"editForm.skills"}})],1),e.editMode?t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-select",{model:{value:e.editForm.status,callback:function(t){e.$set(e.editForm,"status",t)},expression:"editForm.status"}},[t("el-option",{attrs:{label:"在职",value:"active"}}),t("el-option",{attrs:{label:"离职",value:"inactive"}})],1)],1):e._e(),t("el-divider",{attrs:{"content-position":"left"}},[e._v("薪资配置")]),t("el-form-item",{attrs:{label:"基础工资",prop:"base_salary"}},[t("el-input-number",{attrs:{min:0,max:5e4,step:100,placeholder:"基础工资"},model:{value:e.editForm.base_salary,callback:function(t){e.$set(e.editForm,"base_salary",t)},expression:"editForm.base_salary"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("元/月")])],1),t("el-form-item",{attrs:{label:"提成比例",prop:"commission_rate"}},[t("el-input-number",{attrs:{min:0,max:1,step:.01,precision:2,placeholder:"提成比例"},model:{value:e.editForm.commission_rate,callback:function(t){e.$set(e.editForm,"commission_rate",t)},expression:"editForm.commission_rate"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("例如：0.15 表示 15%")])],1),t("el-form-item",{attrs:{label:"绩效奖金比例",prop:"performance_bonus_rate"}},[t("el-input-number",{attrs:{min:0,max:1,step:.01,precision:2,placeholder:"绩效奖金比例"},model:{value:e.editForm.performance_bonus_rate,callback:function(t){e.$set(e.editForm,"performance_bonus_rate",t)},expression:"editForm.performance_bonus_rate"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("例如：0.05 表示 5%")])],1),t("el-form-item",{attrs:{label:"全勤奖",prop:"attendance_bonus"}},[t("el-input-number",{attrs:{min:0,max:5e3,step:50,placeholder:"全勤奖"},model:{value:e.editForm.attendance_bonus,callback:function(t){e.$set(e.editForm,"attendance_bonus",t)},expression:"editForm.attendance_bonus"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("元/月")])],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.editVisible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.saving},on:{click:e.saveUser}},[e._v("保存")])],1)],1)],1)},Ct=[],St={name:"AdminUsers",data(){return{activeUserType:"customers",users:[],loading:!1,saving:!1,searchForm:{username:"",name:"",status:""},currentPage:1,pageSize:20,detailVisible:!1,editVisible:!1,selectedUser:null,editMode:!1,editForm:{username:"",password:"",name:"",phone:"",skills:"",status:"active",base_salary:3e3,commission_rate:.15,performance_bonus_rate:.05,attendance_bonus:200},editRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入电话",trigger:"blur"}],skills:[{required:!0,message:"请输入技能",trigger:"blur"}]}}},computed:{filteredUsers(){let e=this.users;return this.searchForm.username&&(e=e.filter(e=>e.username.includes(this.searchForm.username))),this.searchForm.name&&(e=e.filter(e=>e.name.includes(this.searchForm.name))),this.searchForm.status&&"workers"===this.activeUserType&&(e=e.filter(e=>e.status===this.searchForm.status)),e},paginatedUsers(){const e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;return this.filteredUsers.slice(e,t)}},async created(){await this.loadUsers()},methods:{async loadUsers(){try{this.loading=!0;const e=await this.$store.dispatch("fetchUsers",this.activeUserType);e.success?this.users=this.$store.state.users[this.activeUserType]||[]:this.$message.error(e.message||"获取用户列表失败")}catch(e){console.error("加载用户失败:",e),this.$message.error("加载用户失败")}finally{this.loading=!1}},async handleUserTypeChange(){this.currentPage=1,this.resetSearch(),await this.loadUsers()},async refreshUsers(){await this.loadUsers(),this.$message.success("刷新成功")},searchUsers(){this.currentPage=1},resetSearch(){this.searchForm={username:"",name:"",status:""},this.currentPage=1},showUserDetail(e){this.selectedUser=e,this.detailVisible=!0},showAddDialog(){this.editMode=!1,this.resetEditForm(),this.editVisible=!0},async editUser(e){if(this.editMode=!0,this.editForm={id:e.id,username:e.username,name:e.name,phone:e.phone,skills:e.skills,status:e.status,base_salary:3e3,commission_rate:.15,performance_bonus_rate:.05,attendance_bonus:200},"workers"===this.activeUserType)try{const t=await this.$store.dispatch("fetchWorkerSalaryConfig",e.id);t.success&&t.data&&(this.editForm.base_salary=t.data.base_salary,this.editForm.commission_rate=t.data.commission_rate,this.editForm.performance_bonus_rate=t.data.performance_bonus_rate,this.editForm.attendance_bonus=t.data.attendance_bonus)}catch(t){console.error("加载薪资配置失败:",t)}this.editVisible=!0},resetEditForm(){this.editForm={username:"",password:"",name:"",phone:"",skills:"",status:"active",base_salary:3e3,commission_rate:.15,performance_bonus_rate:.05,attendance_bonus:200},this.$refs.editForm&&this.$refs.editForm.resetFields()},async saveUser(){try{let e;await this.$refs.editForm.validate(),this.saving=!0,e=this.editMode?await this.$store.dispatch("updateUser",{userType:this.activeUserType,userData:this.editForm}):await this.$store.dispatch("createUser",{userType:this.activeUserType,userData:this.editForm}),e.success?(this.$message.success(this.editMode?"更新成功":"添加成功"),this.editVisible=!1,await this.loadUsers()):this.$message.error(e.message||"操作失败")}catch(e){console.error("保存用户失败:",e),this.$message.error("保存失败")}finally{this.saving=!1}},handleSizeChange(e){this.pageSize=e,this.currentPage=1},handleCurrentChange(e){this.currentPage=e},getUserTypeText(){const e={customers:"客户",workers:"工人",admins:"管理员"};return e[this.activeUserType]||"用户"},formatDate(e){if(!e)return"未设置";const t=new Date(e);return t.toLocaleString("zh-CN")},async deleteUser(e){try{await this.$confirm(`确定要删除工人"${e.name}"吗？删除后将无法恢复！`,"警告",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const t=await this.$store.dispatch("deleteUser",{userType:this.activeUserType,userId:e.id});t.success?(this.$message.success("删除成功"),await this.loadUsers()):this.$message.error(t.message||"删除失败")}catch(t){"cancel"!==t&&(console.error("删除用户失败:",t),this.$message.error("删除失败"))}}}},kt=St,Tt=(0,n.A)(kt,wt,Ct,!1,null,"63b56132",null),$t=Tt.exports,Ot=function(){var e=this,t=e._self._c;return t("div",{staticClass:"admin-salary"},[t("el-container",[t("el-header",{staticClass:"admin-header"},[t("div",{staticClass:"header-left"},[t("h2",[e._v("薪资管理")])]),t("div",{staticClass:"header-right"},[t("el-button",{on:{click:function(t){return e.$router.push("/admin/home")}}},[e._v("返回首页")])],1)]),t("el-main",{staticClass:"admin-main"},[t("el-card",{staticClass:"operation-card",attrs:{shadow:"never"}},[t("div",{staticClass:"operation-row"},[t("div",{staticClass:"date-selector"},[t("el-date-picker",{attrs:{type:"month",placeholder:"选择月份",format:"yyyy年MM月","value-format":"yyyy-MM"},on:{change:e.handleMonthChange},model:{value:e.selectedMonth,callback:function(t){e.selectedMonth=t},expression:"selectedMonth"}})],1),t("div",{staticClass:"actions"},[t("el-button",{attrs:{type:"primary",loading:e.calculating},on:{click:e.calculateAllSalaries}},[t("i",{staticClass:"el-icon-calculator"}),e._v(" 计算本月薪资 ")]),t("el-button",{attrs:{type:"success"},on:{click:e.exportSalaries}},[t("i",{staticClass:"el-icon-download"}),e._v(" 导出薪资表 ")])],1)])]),t("el-card",{staticClass:"summary-card",attrs:{header:"薪资汇总"}},[t("div",{staticClass:"summary-stats"},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.summary.totalWorkers))]),t("div",{staticClass:"stat-label"},[e._v("工人总数")])]),t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v("¥"+e._s(e.formatMoney(e.summary.totalSalary)))]),t("div",{staticClass:"stat-label"},[e._v("薪资总额")])]),t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v("¥"+e._s(e.formatMoney(e.summary.totalRevenue)))]),t("div",{staticClass:"stat-label"},[e._v("营收总额")])]),t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.summary.totalOrders))]),t("div",{staticClass:"stat-label"},[e._v("完成工单")])]),t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-value"},[e._v(e._s((parseFloat(e.summary.avgRating)||0).toFixed(1)))]),t("div",{staticClass:"stat-label"},[e._v("平均评分")])])])]),t("el-card",{staticClass:"salary-list-card"},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(e.selectedMonth)+" 薪资明细")]),t("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.loadSalarySummary}},[e._v("刷新")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.salaryList}},[t("el-table-column",{attrs:{prop:"worker_name",label:"工人姓名",width:"100"}}),t("el-table-column",{attrs:{prop:"worker_phone",label:"联系电话",width:"120"}}),t("el-table-column",{attrs:{prop:"completed_orders",label:"完成工单",width:"100",sortable:""}}),t("el-table-column",{attrs:{prop:"total_revenue",label:"营收金额",width:"120",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(e.formatMoney(t.row.total_revenue))+" ")]}}])}),t("el-table-column",{attrs:{prop:"base_salary",label:"基础工资",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(e.formatMoney(t.row.base_salary))+" ")]}}])}),t("el-table-column",{attrs:{prop:"commission_amount",label:"提成收入",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(e.formatMoney(t.row.commission_amount))+" ")]}}])}),t("el-table-column",{attrs:{prop:"performance_bonus",label:"绩效奖金",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(e.formatMoney(t.row.performance_bonus))+" ")]}}])}),t("el-table-column",{attrs:{prop:"attendance_bonus",label:"全勤奖",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(e.formatMoney(t.row.attendance_bonus))+" ")]}}])}),t("el-table-column",{attrs:{prop:"total_salary",label:"总薪资",width:"120",sortable:""},scopedSlots:e._u([{key:"default",fn:function(s){return[t("span",{staticClass:"total-salary"},[e._v("¥"+e._s(e.formatMoney(s.row.total_salary)))])]}}])}),t("el-table-column",{attrs:{prop:"avg_rating",label:"平均评分",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("el-rate",{attrs:{value:parseFloat(e.row.avg_rating)||0,disabled:"","show-score":""}})]}}])}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusType(s.row.status)}},[e._v(" "+e._s(e.getStatusText(s.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{label:"操作",width:"200",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.viewSalaryDetail(s.row)}}},[e._v(" 查看详情 ")]),t("el-button",{attrs:{type:"text",size:"small",loading:e.calculating},on:{click:function(t){return e.calculateSingleSalary(s.row.worker_id)}}},[e._v(" 重新计算 ")]),"draft"===s.row.status?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.confirmSalary(s.row.id)}}},[e._v(" 确认发放 ")]):e._e()]}}])})],1)],1)],1)],1),t("el-dialog",{attrs:{title:"薪资详情",visible:e.detailVisible,width:"800px"},on:{"update:visible":function(t){e.detailVisible=t},close:function(t){e.selectedSalary=null}}},[e.selectedSalary?t("div",{staticClass:"salary-detail"},[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"工人姓名"}},[e._v(e._s(e.selectedSalary.worker_name))]),t("el-descriptions-item",{attrs:{label:"计算月份"}},[e._v(e._s(e.selectedSalary.year)+"年"+e._s(e.selectedSalary.month)+"月")]),t("el-descriptions-item",{attrs:{label:"完成工单"}},[e._v(e._s(e.selectedSalary.completed_orders)+"单")]),t("el-descriptions-item",{attrs:{label:"营收金额"}},[e._v("¥"+e._s(e.formatMoney(e.selectedSalary.total_revenue)))]),t("el-descriptions-item",{attrs:{label:"出勤天数"}},[e._v(e._s(e.selectedSalary.attendance_days)+"天")]),t("el-descriptions-item",{attrs:{label:"平均评分"}},[t("el-rate",{attrs:{value:parseFloat(e.selectedSalary.avg_rating)||0,disabled:"","show-score":""}})],1),t("el-descriptions-item",{attrs:{label:"计算时间"}},[e._v(e._s(e.formatDate(e.selectedSalary.calculated_at)))]),t("el-descriptions-item",{attrs:{label:"状态"}},[t("el-tag",{attrs:{type:e.getStatusType(e.selectedSalary.status)}},[e._v(" "+e._s(e.getStatusText(e.selectedSalary.status))+" ")])],1)],1),t("div",{staticClass:"salary-breakdown"},[t("h4",[e._v("薪资构成")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.salaryDetails}},[t("el-table-column",{attrs:{prop:"item_name",label:"项目",width:"150"}}),t("el-table-column",{attrs:{prop:"amount",label:"金额",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(e.formatMoney(t.row.amount))+" ")]}}],null,!1,3885200678)}),t("el-table-column",{attrs:{prop:"description",label:"说明"}})],1),t("div",{staticClass:"total-row"},[t("strong",[e._v("总计：¥"+e._s(e.formatMoney(e.selectedSalary.total_salary)))])])],1)],1):e._e()])],1)},xt=[],Ft={name:"AdminSalary",data(){return{selectedMonth:this.getCurrentMonth(),loading:!1,calculating:!1,summary:{totalWorkers:0,totalSalary:0,totalRevenue:0,totalOrders:0,avgRating:0},salaryList:[],detailVisible:!1,selectedSalary:null,salaryDetails:[]}},async created(){await this.loadSalarySummary()},methods:{getCurrentMonth(){const e=new Date,t=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0");return`${t}-${s}`},async handleMonthChange(){await this.loadSalarySummary()},async loadSalarySummary(){if(this.selectedMonth)try{this.loading=!0;const[e,t]=this.selectedMonth.split("-"),s=await m.A.get(`/api/salary/summary/${e}/${t}`);s.success?(this.summary=s.data.summary,this.salaryList=s.data.salaries):this.$message.error(s.message||"获取薪资数据失败")}catch(e){console.error("获取薪资汇总失败:",e),this.$message.error("获取薪资数据失败")}finally{this.loading=!1}},async calculateAllSalaries(){if(this.selectedMonth)try{await this.$confirm("确定要计算所有工人的薪资吗？这将覆盖已有的计算结果。","确认计算",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),this.calculating=!0;const[t,s]=this.selectedMonth.split("-"),a=await m.A.get("/api/admin/users/workers");if(!a.success)throw new Error("获取工人列表失败");const r=a.data;let i=0,l=0;for(const o of r)try{await m.A.post(`/api/salary/calculate/${o.id}/${t}/${s}`),i++}catch(e){console.error(`计算工人 ${o.name} 薪资失败:`,e),l++}this.$message.success(`薪资计算完成！成功：${i}人，失败：${l}人`),await this.loadSalarySummary()}catch(e){"cancel"!==e&&(console.error("批量计算薪资失败:",e),this.$message.error("批量计算薪资失败"))}finally{this.calculating=!1}else this.$message.warning("请先选择月份")},async calculateSingleSalary(e){if(this.selectedMonth)try{this.calculating=!0;const[t,s]=this.selectedMonth.split("-"),a=await m.A.post(`/api/salary/calculate/${e}/${t}/${s}`);a.success?(this.$message.success("薪资计算成功"),await this.loadSalarySummary()):this.$message.error(a.message||"薪资计算失败")}catch(t){console.error("计算薪资失败:",t),this.$message.error("薪资计算失败")}finally{this.calculating=!1}else this.$message.warning("请先选择月份")},async viewSalaryDetail(e){try{this.loading=!0;const t=await m.A.get(`/api/salary/detail/${e.id}`);t.success?(this.selectedSalary=t.data.salary,this.salaryDetails=t.data.details,this.detailVisible=!0):this.$message.error(t.message||"获取薪资详情失败")}catch(t){console.error("获取薪资详情失败:",t),this.$message.error("获取薪资详情失败")}finally{this.loading=!1}},async confirmSalary(e){try{await this.$confirm("确定要确认发放这笔薪资吗？","确认发放",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),this.$message.success("薪资确认发放成功"),await this.loadSalarySummary()}catch(t){"cancel"!==t&&this.$message.error("确认发放失败")}},exportSalaries(){this.$message.info("导出功能开发中...")},getStatusType(e){const t={draft:"info",confirmed:"warning",paid:"success"};return t[e]||"info"},getStatusText(e){const t={draft:"草稿",confirmed:"已确认",paid:"已发放"};return t[e]||e},formatMoney(e){return e?parseFloat(e).toFixed(2):"0.00"},formatDate(e){if(!e)return"未设置";const t=new Date(e);return t.toLocaleString("zh-CN")}}},At=Ft,Dt=(0,n.A)(At,Ot,xt,!1,null,"60d00e03",null),Ut=Dt.exports,Rt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"home-page"},[e._m(0),t("div",{staticClass:"hero-section"},[t("div",{staticClass:"hero-content"},[e._m(1),t("div",{staticClass:"portal-cards"},[t("div",{staticClass:"portal-card customer-card",on:{click:function(t){return e.goToPortal("customer")}}},[t("div",{staticClass:"card-background"}),e._m(2)]),t("div",{staticClass:"portal-card worker-card",on:{click:function(t){return e.goToPortal("worker")}}},[t("div",{staticClass:"card-background"}),e._m(3)]),t("div",{staticClass:"portal-card admin-card",on:{click:function(t){return e.goToPortal("admin")}}},[t("div",{staticClass:"card-background"}),e._m(4)])])])]),t("div",{staticClass:"features-section"},[t("div",{staticClass:"container"},[e._m(5),t("div",{staticClass:"features-grid"},e._l(e.features,function(s,a){return t("div",{key:a,staticClass:"feature-item",style:{animationDelay:.1*a+"s"}},[t("div",{staticClass:"feature-icon"},[t("i",{class:s.icon})]),t("h4",[e._v(e._s(s.title))]),t("p",[e._v(e._s(s.description))])])}),0)])])])},zt=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"background-decoration"},[t("div",{staticClass:"floating-shape shape-1"}),t("div",{staticClass:"floating-shape shape-2"}),t("div",{staticClass:"floating-shape shape-3"}),t("div",{staticClass:"floating-shape shape-4"})])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"title-section"},[t("h1",{staticClass:"main-title"},[t("span",{staticClass:"title-line"},[e._v("永盛制冷维修")]),t("span",{staticClass:"title-line"},[e._v("有限公司")])]),t("p",{staticClass:"subtitle"},[e._v("专业的家电售卖与维修服务管理平台")]),t("div",{staticClass:"title-decoration"})])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"card-content"},[t("div",{staticClass:"card-icon"},[t("i",{staticClass:"el-icon-user"})]),t("h3",[e._v("客户端")]),t("p",[e._v("报修家电故障，查看维修进度")]),t("div",{staticClass:"card-button"},[t("span",[e._v("进入客户端")]),t("i",{staticClass:"el-icon-arrow-right"})])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"card-content"},[t("div",{staticClass:"card-icon"},[t("i",{staticClass:"el-icon-s-tools"})]),t("h3",[e._v("工人端")]),t("p",[e._v("接单维修，记录维修过程")]),t("div",{staticClass:"card-button"},[t("span",[e._v("进入工人端")]),t("i",{staticClass:"el-icon-arrow-right"})])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"card-content"},[t("div",{staticClass:"card-icon"},[t("i",{staticClass:"el-icon-s-platform"})]),t("h3",[e._v("管理后台")]),t("p",[e._v("管理工单，查看统计报表")]),t("div",{staticClass:"card-button"},[t("span",[e._v("进入管理后台")]),t("i",{staticClass:"el-icon-arrow-right"})])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"section-header"},[t("h2",[e._v("系统特色")]),t("p",[e._v("为您提供全方位的家电维修服务解决方案")])])}],Pt={name:"HomePage",data(){return{features:[{icon:"el-icon-mobile-phone",title:"便捷报修",description:"客户可以快速提交维修申请，上传故障图片"},{icon:"el-icon-time",title:"实时跟踪",description:"实时查看维修进度，掌握工单状态"},{icon:"el-icon-s-cooperation",title:"高效派单",description:"智能匹配维修师傅，提高服务效率"},{icon:"el-icon-data-analysis",title:"数据分析",description:"完整的数据统计和分析报表"}]}},mounted(){console.log("[前端] HomePage组件已挂载");const e=this.$store.getters.currentUser;console.log("[前端] 当前用户状态:",e),window.clearAuth=()=>{console.log("[前端] 手动清除认证信息"),localStorage.removeItem("token"),localStorage.removeItem("user"),this.$store.dispatch("logout"),location.reload()},console.log("[前端] 可以在控制台使用 clearAuth() 清除认证信息")},methods:{goToPortal(e){console.log("[前端] 点击进入端口:",e);const t=this.$store.getters.currentUser;console.log("[前端] 当前用户:",t),t&&t.userType===e?(console.log("[前端] 用户已登录相同类型，跳转到主页:",`/${e}/home`),this.$router.push(`/${e}/home`)):(t&&t.userType&&t.userType!==e&&(console.log("[前端] 用户已登录不同类型，先退出登录"),this.$store.dispatch("logout")),console.log("[前端] 跳转到登录页:",`/${e}/login`),this.$router.push(`/${e}/login`))}}},Lt=Pt,Mt=(0,n.A)(Lt,Rt,zt,!1,null,"348d3b7e",null),Et=Mt.exports;a["default"].use(u.Ay);const It=[{path:"/",name:"HomePage",component:Et},{path:"/customer",redirect:"/customer/login"},{path:"/customer/login",name:"CustomerLogin",component:y,meta:{requiresGuest:!0,userType:"customer"}},{path:"/customer/register",name:"CustomerRegister",component:$,meta:{requiresGuest:!0,userType:"customer"}},{path:"/customer/home",name:"CustomerHome",component:U,meta:{requiresAuth:!0,userType:"customer"}},{path:"/customer/orders",name:"CustomerOrders",component:J,meta:{requiresAuth:!0,userType:"customer"}},{path:"/customer/create-order",name:"CustomerCreateOrder",component:se,meta:{requiresAuth:!0,userType:"customer"}},{path:"/worker",redirect:"/worker/login"},{path:"/worker/login",name:"WorkerLogin",component:ne,meta:{requiresGuest:!0,userType:"worker"}},{path:"/worker/home",name:"WorkerHome",component:he,meta:{requiresAuth:!0,userType:"worker"}},{path:"/worker/orders",name:"WorkerOrders",component:Ee,meta:{requiresAuth:!0,userType:"worker"}},{path:"/worker/salary",name:"WorkerSalary",component:Be,meta:{requiresAuth:!0,userType:"worker"}},{path:"/worker/available-orders",name:"WorkerAvailableOrders",component:Je,meta:{requiresAuth:!0,userType:"worker"}},{path:"/admin",redirect:"/admin/login"},{path:"/admin/login",name:"AdminLogin",component:st,meta:{requiresGuest:!0,userType:"admin"}},{path:"/admin/home",name:"AdminHome",component:nt,meta:{requiresAuth:!0,userType:"admin"}},{path:"/admin/orders",name:"AdminOrders",component:ht,meta:{requiresAuth:!0,userType:"admin"}},{path:"/admin/statistics",name:"AdminStatistics",component:yt,meta:{requiresAuth:!0,userType:"admin"}},{path:"/admin/users",name:"AdminUsers",component:$t,meta:{requiresAuth:!0,userType:"admin"}},{path:"/admin/salary",name:"AdminSalary",component:Ut,meta:{requiresAuth:!0,userType:"admin"}}],Nt=new u.Ay({mode:"history",base:"/",routes:It});Nt.beforeEach((e,t,s)=>{if(console.log("[前端] 路由守卫开始 - from:",t.path,"to:",e.path),e.path===t.path)return console.log("[前端] 检测到相同路径，跳过处理"),s();const a=localStorage.getItem("token"),r=JSON.parse(localStorage.getItem("user")||"{}"),i=!!a&&r.userType;if(console.log("[前端] 认证状态:",{hasToken:!!a,userType:r.userType,isAuthenticated:i,toPath:e.path,toMeta:e.meta}),e.matched.some(e=>e.meta.requiresAuth)){if(console.log("[前端] 访问需要认证的路由"),!i){console.log("[前端] 用户未认证，准备重定向到登录页");const t=e.meta.userType;return t&&e.path!==`/${t}/login`?(console.log("[前端] 重定向到:",`/${t}/login`),s(`/${t}/login`)):(console.log("[前端] 重定向到首页"),s("/"))}if(e.meta.userType&&r.userType!==e.meta.userType)if(console.log("[前端] 用户类型不匹配，当前用户类型:",r.userType,"需要类型:",e.meta.userType),r.userType&&["customer","worker","admin"].includes(r.userType)){const t=`/${r.userType}/home`;if(e.path!==t)return console.log("[前端] 重定向到用户主页:",t),s(t)}else if(console.log("[前端] 用户类型无效，清除认证信息"),localStorage.removeItem("token"),localStorage.removeItem("user"),"/"!==e.path)return console.log("[前端] 重定向到首页"),s("/");return console.log("[前端] 认证通过，继续访问"),s()}if(e.matched.some(e=>e.meta.requiresGuest)){if(console.log("[前端] 访问需要游客状态的路由"),i&&r.userType){console.log("[前端] 已登录用户访问登录页");const t=e.meta.userType;if(t===r.userType){const t=`/${r.userType}/home`;if(e.path!==t)return console.log("[前端] 重定向到相同类型用户主页:",t),s(t)}else console.log("[前端] 用户尝试切换到不同类型登录页，清除当前认证信息"),localStorage.removeItem("token"),localStorage.removeItem("user"),h&&h.commit&&h.commit("CLEAR_AUTH")}return console.log("[前端] 游客状态检查通过"),s()}console.log("[前端] 普通路由，直接通过"),s()});var qt=Nt,Vt=s(1052),Gt=s.n(Vt),Bt=s(4603);s(1394),s(1855),s(4134),s(2450),s(9921),s(4941);a["default"].config.productionTip=!1,a["default"].use(Gt()),a["default"].component("v-chart",Bt.Ay),m.A.defaults.baseURL={NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_API_BASE_URL||"/api",m.A.defaults.timeout=1e4,m.A.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),m.A.interceptors.response.use(e=>(console.log("[前端] API响应成功:",e.config.url),e.data),e=>{if(console.log("[前端] API响应错误:",e.config?.url,e.response?.status),e.response)switch(e.response.status){case 401:const t=e.response?.data?.message||"",s=t.includes("token")||t.includes("过期")||t.includes("无效")||t.includes("expired")||t.includes("invalid")||t.includes("unauthorized");s?(console.log("[前端] Token过期或无效，清除认证信息"),localStorage.removeItem("token"),localStorage.removeItem("user"),h.commit("CLEAR_AUTH"),Gt().Message.error("登录已过期，请重新登录")):(console.log("[前端] 401错误但非token问题，不清除认证信息"),Gt().Message.error(t||"请求失败，请检查权限"));break;case 403:console.log("[前端] 403错误，权限不足"),Gt().Message.error("权限不足");break;case 500:console.log("[前端] 500错误，服务器错误"),Gt().Message.error("服务器错误");break;default:console.log("[前端] 其他HTTP错误:",e.response.status),Gt().Message.error(e.response.data.message||"请求失败")}else console.log("[前端] 网络错误:",e.message),Gt().Message.error("网络错误");return Promise.reject(e)}),a["default"].prototype.$http=m.A,a["default"].prototype.$safeRouter={push(e){return qt.push(e).catch(e=>{"NavigationDuplicated"!==e.name&&console.error("路由跳转错误:",e)})},replace(e){return qt.replace(e).catch(e=>{"NavigationDuplicated"!==e.name&&console.error("路由跳转错误:",e)})}},new a["default"]({router:qt,store:h,render:e=>e(d)}).$mount("#app")}},t={};function s(a){var r=t[a];if(void 0!==r)return r.exports;var i=t[a]={id:a,loaded:!1,exports:{}};return e[a](i,i.exports,s),i.loaded=!0,i.exports}s.m=e,function(){s.amdO={}}(),function(){var e=[];s.O=function(t,a,r,i){if(!a){var l=1/0;for(d=0;d<e.length;d++){a=e[d][0],r=e[d][1],i=e[d][2];for(var o=!0,n=0;n<a.length;n++)(!1&i||l>=i)&&Object.keys(s.O).every(function(e){return s.O[e](a[n])})?a.splice(n--,1):(o=!1,i<l&&(l=i));if(o){e.splice(d--,1);var c=r();void 0!==c&&(t=c)}}return t}i=i||0;for(var d=e.length;d>0&&e[d-1][2]>i;d--)e[d]=e[d-1];e[d]=[a,r,i]}}(),function(){s.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(t,{a:t}),t}}(),function(){s.d=function(e,t){for(var a in t)s.o(t,a)&&!s.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}}(),function(){s.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){s.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){var e={524:0};s.O.j=function(t){return 0===e[t]};var t=function(t,a){var r,i,l=a[0],o=a[1],n=a[2],c=0;if(l.some(function(t){return 0!==e[t]})){for(r in o)s.o(o,r)&&(s.m[r]=o[r]);if(n)var d=n(s)}for(t&&t(a);c<l.length;c++)i=l[c],s.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return s.O(d)},a=self["webpackChunkappliance_repair_frontend"]=self["webpackChunkappliance_repair_frontend"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))}();var a=s.O(void 0,[504],function(){return s(7816)});a=s.O(a)})();
//# sourceMappingURL=app.baa070d1.js.map