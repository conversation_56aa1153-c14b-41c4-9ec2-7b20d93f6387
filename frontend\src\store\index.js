import Vue from 'vue'
import Vuex from 'vuex'
import axios from 'axios'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    user: JSON.parse(localStorage.getItem('user')) || null,
    token: localStorage.getItem('token') || null,
    orders: [],
    availableOrders: [],
    statistics: {},
    loading: false
  },
  
  getters: {
    isAuthenticated: state => !!state.token,
    currentUser: state => state.user,
    userType: state => state.user?.userType || null,
    userOrders: state => state.orders,
    availableOrders: state => state.availableOrders,
    statistics: state => state.statistics
  },
  
  mutations: {
    SET_USER(state, user) {
      state.user = user
      localStorage.setItem('user', JSON.stringify(user))
    },
    
    SET_TOKEN(state, token) {
      state.token = token
      localStorage.setItem('token', token)
    },
    
    CLEAR_AUTH(state) {
      // 用于token过期等被动清除认证
      state.user = null
      state.token = null
      localStorage.removeItem('user')
      localStorage.removeItem('token')
    },

    LOGOUT(state) {
      // 用于主动退出登录
      state.user = null
      state.token = null
      state.userType = null
      localStorage.removeItem('user')
      localStorage.removeItem('token')
      localStorage.removeItem('userType')
    },
    
    SET_ORDERS(state, orders) {
      state.orders = orders
    },
    
    SET_AVAILABLE_ORDERS(state, orders) {
      state.availableOrders = orders
    },

    ADD_ORDER(state, order) {
      state.orders.unshift(order)
    },

    SET_USERS(state, { userType, users }) {
      if (!state.users) {
        state.users = {}
      }
      state.users[userType] = users
    },
    
    UPDATE_ORDER(state, updatedOrder) {
      const index = state.orders.findIndex(order => order.id === updatedOrder.id)
      if (index !== -1) {
        Vue.set(state.orders, index, updatedOrder)
      }
    },
    
    SET_STATISTICS(state, statistics) {
      state.statistics = statistics
    },
    
    SET_LOADING(state, loading) {
      state.loading = loading
    }
  },
  
  actions: {
    async login({ commit }, { credentials, userType }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 登录请求:', { userType, username: credentials.username })

        const response = await axios.post('/api/api/auth/login', {
          ...credentials,
          userType
        })

        console.log('[前端] 登录响应:', response)

        // 由于axios拦截器返回了response.data，所以response就是后端的响应数据
        if (response.success) {
          const { user, token } = response.data

          // 存储到localStorage
          localStorage.setItem('token', token)
          localStorage.setItem('user', JSON.stringify(user))
          localStorage.setItem('userType', userType)

          // 设置axios默认header
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`

          // 更新store状态
          commit('SET_USER', user)
          commit('SET_TOKEN', token)

          return { success: true, user, userType }
        } else {
          return { success: false, message: response.message }
        }
      } catch (error) {
        console.error('[前端] 登录失败:', error)
        return {
          success: false,
          message: error.response?.data?.message || '登录失败，请检查网络连接'
        }
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    async register({ commit }, userData) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 注册请求:', userData)

        const response = await axios.post('/api/auth/register', userData)

        console.log('[前端] 注册响应:', response)
        console.log('[前端] 响应类型:', typeof response)
        console.log('[前端] 响应success字段:', response.success)

        if (response.success) {
          console.log('[前端] 注册成功，返回成功结果')
          return { success: true, message: response.message }
        } else {
          console.log('[前端] 注册失败，返回失败结果')
          return { success: false, message: response.message }
        }
      } catch (error) {
        console.error('[前端] 注册失败:', error)
        return {
          success: false,
          message: error.response?.data?.message || '注册失败，请检查网络连接'
        }
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    logout({ commit }) {
      // 主动退出登录
      commit('LOGOUT')
      return { success: true, message: '退出登录成功' }
    },
    
    // 客户端相关actions
    async fetchCustomerOrders({ commit }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 获取客户工单列表...')
        const response = await axios.get('/api/customer/orders')

        console.log('[前端] 获取客户工单成功:', response.data)
        commit('SET_ORDERS', response.data.data || response.data)

        return { success: true }
      } catch (error) {
        console.error('[前端] 获取工单失败:', error)
        return { success: false, message: error.response?.data?.message || '获取工单失败' }
      } finally {
        commit('SET_LOADING', false)
      }
    },

    async createOrder({ commit }, orderData) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 创建工单请求:', orderData)

        // 准备API数据格式
        const apiData = {
          appliance_type: orderData.appliance_type,
          brand_model: orderData.brand_model,
          problem_description: orderData.problem_description,
          contact_name: orderData.contact_name,
          contact_phone: orderData.contact_phone,
          address: orderData.address,
          purchase_date: orderData.purchase_date,
          preferred_date: orderData.preferred_date,
          preferred_time: orderData.preferred_time,
          urgency: orderData.urgency,
          remarks: orderData.remarks
        }

        const response = await axios.post('/api/customer/orders', apiData)

        console.log('[前端] 工单创建成功:', response.data)

        return { success: true, data: response.data.data || response.data }
      } catch (error) {
        console.error('[前端] 创建工单失败:', error)
        return {
          success: false,
          message: error.response?.data?.message || '创建工单失败'
        }
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    // 工人端相关actions
    async fetchAvailableOrders({ commit }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 获取可接工单列表...')
        const response = await axios.get('/api/worker/available-orders')

        console.log('[前端] 获取可接工单成功:', response)
        commit('SET_AVAILABLE_ORDERS', response.data || response)

        return { success: true }
      } catch (error) {
        console.error('[前端] 获取可接工单失败:', error)
        return { success: false, message: error.response?.data?.message || '获取可接工单失败' }
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    async fetchWorkerOrders({ commit }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 获取工人工单列表...')
        const response = await axios.get('/api/worker/my-orders')

        console.log('[前端] 获取工人工单成功:', response)
        commit('SET_ORDERS', response.data || response)

        return { success: true }
      } catch (error) {
        console.error('[前端] 获取工人工单失败:', error)
        return { success: false, message: error.response?.data?.message || '获取工单失败' }
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    async acceptOrder({ commit }, orderId) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 接单请求:', orderId)

        const response = await axios.post(`/api/worker/accept-order/${orderId}`)

        console.log('[前端] 接单成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 接单失败:', error)
        return {
          success: false,
          message: error.response?.data?.message || '接单失败'
        }
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    // 管理端相关actions
    async fetchAllOrders({ commit }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 获取所有工单列表...')
        const response = await axios.get('/api/admin/orders')

        console.log('[前端] 获取所有工单成功:', response)
        commit('SET_ORDERS', response.data || response)

        return { success: true }
      } catch (error) {
        console.error('[前端] 获取所有工单失败:', error)
        return { success: false, message: error.response?.data?.message || '获取所有工单失败' }
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    async fetchStatistics({ commit }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 获取统计数据...')
        const response = await axios.get('/api/admin/statistics')

        console.log('[前端] 获取统计数据成功:', response)
        commit('SET_STATISTICS', response.data || response)

        return { success: true }
      } catch (error) {
        console.error('[前端] 获取统计数据失败:', error)
        return { success: false, message: error.response?.data?.message || '获取统计数据失败' }
      } finally {
        commit('SET_LOADING', false)
      }
    },

    // 获取用户列表
    async fetchUsers({ commit }, userType) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 获取用户列表...', userType)
        const response = await axios.get(`/api/admin/users/${userType}`)

        console.log('[前端] 获取用户列表成功:', response)
        commit('SET_USERS', { userType, users: response.data || response })

        return { success: true }
      } catch (error) {
        console.error('[前端] 获取用户列表失败:', error)
        return { success: false, message: error.response?.data?.message || '获取用户列表失败' }
      } finally {
        commit('SET_LOADING', false)
      }
    },

    // 创建用户
    async createUser({ commit }, { userType, userData }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 创建用户...', userType, userData)
        const response = await axios.post(`/api/admin/users/${userType}`, userData)

        console.log('[前端] 创建用户成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 创建用户失败:', error)
        return { success: false, message: error.response?.data?.message || '创建用户失败' }
      } finally {
        commit('SET_LOADING', false)
      }
    },

    // 更新用户
    async updateUser({ commit }, { userType, userData }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 更新用户...', userType, userData)
        const response = await axios.put(`/api/admin/users/${userType}/${userData.id}`, userData)

        console.log('[前端] 更新用户成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 更新用户失败:', error)
        return { success: false, message: error.response?.data?.message || '更新用户失败' }
      } finally {
        commit('SET_LOADING', false)
      }
    },

    // 删除用户
    async deleteUser({ commit }, { userType, userId }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 删除用户...', userType, userId)
        const response = await axios.delete(`/api/admin/users/${userType}/${userId}`)

        console.log('[前端] 删除用户成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 删除用户失败:', error)
        return { success: false, message: error.response?.data?.message || '删除用户失败' }
      } finally {
        commit('SET_LOADING', false)
      }
    },

    // 获取工人薪资配置
    async fetchWorkerSalaryConfig({ commit }, workerId) {
      try {
        console.log('[前端] 获取工人薪资配置...', workerId)
        const response = await axios.get(`/api/salary/config/${workerId}`)

        console.log('[前端] 获取薪资配置成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 获取薪资配置失败:', error)
        return { success: false, message: error.response?.data?.message || '获取薪资配置失败' }
      }
    },





    // 获取工人统计数据
    async fetchWorkerStats({ commit }) {
      try {
        console.log('[前端] 获取工人统计数据...')

        const response = await axios.get('/api/worker/stats')

        console.log('[前端] 获取工人统计数据成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 获取工人统计数据失败:', error)
        return { success: false, message: error.response?.data?.message || '获取工人统计数据失败' }
      }
    },

    // 获取工单状态统计
    async fetchOrderStatusStats({ commit }) {
      try {
        console.log('[前端] 获取工单状态统计...')

        const response = await axios.get('/api/admin/stats/order-status')

        console.log('[前端] 获取工单状态统计成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 获取工单状态统计失败:', error)
        return { success: false, message: error.response?.data?.message || '获取工单状态统计失败' }
      }
    },

    // 获取设备类型统计
    async fetchApplianceTypeStats({ commit }) {
      try {
        console.log('[前端] 获取设备类型统计...')

        const response = await axios.get('/api/admin/stats/appliance-type')

        console.log('[前端] 获取设备类型统计成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 获取设备类型统计失败:', error)
        return { success: false, message: error.response?.data?.message || '获取设备类型统计失败' }
      }
    },

    // 获取工人绩效统计
    async fetchWorkerPerformanceStats({ commit }) {
      try {
        console.log('[前端] 获取工人绩效统计...')

        const response = await axios.get('/api/admin/stats/worker-performance')

        console.log('[前端] 获取工人绩效统计成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 获取工人绩效统计失败:', error)
        return { success: false, message: error.response?.data?.message || '获取工人绩效统计失败' }
      }
    },

    // 获取月度趋势统计
    async fetchMonthlyTrendStats({ commit }) {
      try {
        console.log('[前端] 获取月度趋势统计...')

        const response = await axios.get('/api/admin/stats/monthly-trend')

        console.log('[前端] 获取月度趋势统计成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 获取月度趋势统计失败:', error)
        return { success: false, message: error.response?.data?.message || '获取月度趋势统计失败' }
      }
    },

    async fetchAvailableOrders({ commit }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 获取可接工单列表...')
        const response = await axios.get('/api/worker/available-orders')

        console.log('[前端] 获取可接工单成功:', response.data)
        commit('SET_AVAILABLE_ORDERS', response.data.data || response.data)

        return { success: true }
      } catch (error) {
        console.error('[前端] 获取可接工单失败:', error)
        return { success: false, message: error.response?.data?.message || '获取可接工单失败' }
      } finally {
        commit('SET_LOADING', false)
      }
    },

    async acceptOrder({ commit }, orderId) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 接单请求:', orderId)

        const response = await axios.post(`/api/worker/accept-order/${orderId}`)

        console.log('[前端] 接单成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 接单失败:', error)
        return {
          success: false,
          message: error.response?.data?.message || '接单失败'
        }
      } finally {
        commit('SET_LOADING', false)
      }
    },

    // 开始维修
    async startRepair({ commit }, orderId) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 开始维修请求:', orderId)

        const response = await axios.post(`/api/worker/start-repair/${orderId}`)

        console.log('[前端] 开始维修成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 开始维修失败:', error)
        return {
          success: false,
          message: error.response?.data?.message || '开始维修失败'
        }
      } finally {
        commit('SET_LOADING', false)
      }
    },

    // 更新维修进度
    async updateProgress({ commit }, { orderId, progressData }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 更新进度请求:', orderId, progressData)

        const response = await axios.post(`/api/worker/update-progress/${orderId}`, progressData)

        console.log('[前端] 更新进度成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 更新进度失败:', error)
        return {
          success: false,
          message: error.response?.data?.message || '更新进度失败'
        }
      } finally {
        commit('SET_LOADING', false)
      }
    },

    // 完成维修
    async completeRepair({ commit }, { orderId, repairData }) {
      try {
        commit('SET_LOADING', true)

        console.log('[前端] 完成维修请求:', orderId, repairData)

        const response = await axios.post(`/api/worker/complete-repair/${orderId}`, repairData)

        console.log('[前端] 完成维修成功:', response)

        return { success: true, data: response.data || response }
      } catch (error) {
        console.error('[前端] 完成维修失败:', error)
        return {
          success: false,
          message: error.response?.data?.message || '完成维修失败'
        }
      } finally {
        commit('SET_LOADING', false)
      }
    }
  }
})



